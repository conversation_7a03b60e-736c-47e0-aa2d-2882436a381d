#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的 Cursor Pro 发布包
修复打包问题，确保结构清晰
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_release_directory():
    """创建发布目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = Path(f'cursor_pro_fixed_{timestamp}')
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    print(f"📁 创建修复版目录: {release_dir}")
    return release_dir

def copy_correct_components(release_dir):
    """复制正确的组件"""
    print("\n📦 复制正确的组件...")
    
    # 1. 复制主启动器到根目录 (优先使用智能启动器)
    if Path('dist/Cursor Pro 智能启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 智能启动器.exe', release_dir / 'Cursor Pro.exe')
        print("✅ 复制智能启动器为主启动器")
    elif Path('dist/Cursor Pro 启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 启动器.exe', release_dir / 'Cursor Pro.exe')
        print("✅ 复制标准启动器为主启动器")
    
    # 2. 复制前端应用
    if Path('cursor_pro/Cursor Pro.exe').exists():
        frontend_dir = release_dir / 'frontend'
        frontend_dir.mkdir()
        
        # 复制前端主程序
        shutil.copy2('cursor_pro/Cursor Pro.exe', frontend_dir / 'Cursor Pro.exe')
        
        # 复制前端依赖文件
        frontend_files = [
            'chrome_100_percent.pak', 'chrome_200_percent.pak', 'resources.pak',
            'snapshot_blob.bin', 'v8_context_snapshot.bin', 'icudtl.dat',
            'd3dcompiler_47.dll', 'ffmpeg.dll', 'libEGL.dll', 'libGLESv2.dll',
            'vk_swiftshader.dll', 'vulkan-1.dll', 'vk_swiftshader_icd.json',
            'LICENSES.chromium.html'
        ]
        
        for file in frontend_files:
            src_path = Path('cursor_pro') / file
            if src_path.exists():
                shutil.copy2(src_path, frontend_dir / file)
        
        # 复制前端目录
        frontend_dirs = ['locales', 'resources']
        for dir_name in frontend_dirs:
            src_dir = Path('cursor_pro') / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, frontend_dir / dir_name, dirs_exist_ok=True)
        
        print("✅ 复制前端应用和依赖")
    
    # 3. 复制后端服务 (使用最新构建的)
    if Path('dist/cursor-backend.exe').exists():
        backend_dir = release_dir / 'backend'
        backend_dir.mkdir()
        
        shutil.copy2('dist/cursor-backend.exe', backend_dir / 'cursor-backend.exe')
        
        # 复制后端依赖
        backend_dirs = ['api', 'database', 'locales', 'images']
        for dir_name in backend_dirs:
            src_dir = Path('dist') / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, backend_dir / dir_name, dirs_exist_ok=True)
        
        # 复制后端配置文件
        backend_files = ['requirements.txt']
        for file in backend_files:
            src_path = Path('dist') / file
            if src_path.exists():
                shutil.copy2(src_path, backend_dir / file)
        
        print("✅ 复制后端服务 (最新版本)")
    
    # 4. 创建工具目录
    tools_dir = release_dir / 'tools'
    tools_dir.mkdir()
    
    if Path('cleanup_cursor_backend.bat').exists():
        shutil.copy2('cleanup_cursor_backend.bat', tools_dir / '清理后端进程.bat')
        print("✅ 复制清理工具 (重命名)")
    
    # 5. 复制文档
    if Path('docs').exists():
        shutil.copytree('docs', release_dir / 'docs', dirs_exist_ok=True)
        print("✅ 复制文档目录")
    
    # 6. 复制重要文件
    important_files = ['README.md', '快速上手.md']
    for file in important_files:
        if Path(file).exists():
            shutil.copy2(file, release_dir / file)
            print(f"✅ 复制: {file}")

def create_startup_script(release_dir):
    """创建启动脚本"""
    startup_content = '''@echo off
chcp 65001 >nul
title Cursor Pro
color 0A

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗ ██████╗ ██████╗     ██████╗ ██████╗  ██████╗ 
echo ██╔════╝██║   ██║██╔══██╗██╔════╝██╔═══██╗██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗
echo ██║     ██║   ██║██████╔╝███████╗██║   ██║██████╔╝    ██████╔╝██████╔╝██║   ██║
echo ██║     ██║   ██║██╔══██╗╚════██║██║   ██║██╔══██╗    ██╔═══╝ ██╔══██╗██║   ██║
echo ╚██████╗╚██████╔╝██║  ██║███████║╚██████╔╝██║  ██║    ██║     ██║  ██║╚██████╔╝
echo  ╚═════╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝    ╚═╝     ╚═╝  ╚═╝ ╚═════╝ 
echo.
echo                           增强版 - 智能启动系统
echo.

echo 🚀 正在启动 Cursor Pro...
echo.

REM 启动主程序
start "" "Cursor Pro.exe"

echo ✅ Cursor Pro 已启动
echo.
echo 💡 提示:
echo    - 如需清理后端进程，运行: tools\\清理后端进程.bat
echo    - 查看文档: docs\\ 目录
echo.
echo 按任意键关闭此窗口...
pause >nul
'''
    
    startup_path = release_dir / 'start.bat'
    with open(startup_path, 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ 创建启动脚本: start.bat")

def create_readme(release_dir):
    """创建使用说明"""
    readme_content = '''# Cursor Pro - 修复版

## 🚀 快速启动

### 方法1: 双击启动 (推荐)
双击 `Cursor Pro.exe` 即可启动

### 方法2: 使用启动脚本
双击 `start.bat` 启动 (带提示信息)

## 📁 目录结构

```
Cursor Pro/
├── Cursor Pro.exe          # 主启动器 (双击这个)
├── start.bat              # 启动脚本 (可选)
├── frontend/               # 前端应用
├── backend/                # 后端服务
├── tools/                  # 工具目录
│   └── 清理后端进程.bat    # 进程清理工具
├── docs/                   # 完整文档
└── README.md               # 详细说明
```

## 🔧 故障排除

**如果遇到后端进程残留:**
运行 `tools\\清理后端进程.bat`

**如果启动失败:**
1. 检查是否有杀毒软件拦截
2. 以管理员身份运行
3. 查看 `docs\\技术支持.md`

## 📖 更多信息

- 详细文档: `docs/` 目录
- 快速上手: `快速上手.md`
- 技术支持: `docs\\技术支持.md`

---
**Cursor Pro Enhanced Edition - Fixed Version**
'''
    
    readme_path = release_dir / 'README.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建使用说明: README.txt")

def create_zip_package(release_dir):
    """创建 ZIP 压缩包"""
    print("\n🗜️ 创建修复版压缩包...")
    
    zip_name = f'{release_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 Cursor Pro - 修复版打包工具")
    print("=" * 60)
    print("修复问题:")
    print("- 移除重复文件")
    print("- 统一启动器")
    print("- 清晰的目录结构")
    print("- 重命名工具文件")
    print("=" * 60)
    
    # 创建发布目录
    release_dir = create_release_directory()
    
    # 复制正确的组件
    copy_correct_components(release_dir)
    
    # 创建脚本和文档
    create_startup_script(release_dir)
    create_readme(release_dir)
    
    # 创建压缩包
    zip_file = create_zip_package(release_dir)
    
    print("\n" + "=" * 60)
    print("🎉 修复版打包完成!")
    print("=" * 60)
    print(f"📁 修复版目录: {release_dir}")
    print(f"📦 压缩包: {zip_file}")
    print("\n✅ 修复的问题:")
    print("- 只有一个主启动器: Cursor Pro.exe")
    print("- 清晰的目录结构: frontend/ backend/ tools/")
    print("- 工具重命名: 清理后端进程.bat")
    print("- 移除重复文件")
    print("\n🚀 用户使用:")
    print("1. 解压文件")
    print("2. 双击 Cursor Pro.exe")
    print("3. 完成!")

if __name__ == "__main__":
    main()
