// 管理员API - 用于发布新版本和控制用户更新
const fs = require('fs');
const path = require('path');

module.exports = (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Admin-Key');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // 简单的管理员验证（在生产环境中应该使用更安全的方法）
  const adminKey = req.headers['x-admin-key'] || req.query.admin_key;
  const validAdminKey = process.env.ADMIN_KEY || 'cursor-admin-2025'; // 可以在Vercel环境变量中设置
  
  if (adminKey !== validAdminKey) {
    return res.status(401).json({
      status: "error",
      message: "未授权访问，需要管理员密钥"
    });
  }

  // 使用环境变量存储配置（Vercel兼容）
  const getConfig = () => {
    try {
      // 尝试从环境变量读取配置
      const envConfig = process.env.VERSION_CONFIG;
      if (envConfig) {
        return JSON.parse(envConfig);
      }

      // 如果环境变量不存在，尝试读取文件（本地开发）
      const configPath = path.join(process.cwd(), 'api', 'version-config.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
      }

      // 返回默认配置
      return {
        version_control: {
          latest_version: "1.0.0",
          min_supported_version: "1.0.0",
          force_update: false,
          update_message: "发现新版本，建议及时更新以获得最佳体验！",
          download_message: "请到QQ群/微信群下载最新版本",
          download_url: "https://github.com/tul345/cursor-pro-main/releases",
          maintenance_mode: false
        },
        version_history: [
          {
            version: "1.0.0",
            release_date: "2025-01-20",
            description: "初始版本发布",
            critical: false
          }
        ],
        blocked_versions: [],
        admin_notes: "使用环境变量存储配置"
      };
    } catch (error) {
      console.error('获取配置失败:', error);
      return null;
    }
  };

  try {
    if (req.method === 'GET') {
      // 获取当前配置
      const config = getConfig();
      
      if (!config) {
        return res.status(500).json({
          status: "error",
          message: "无法获取配置"
        });
      }

      res.json({
        status: "success",
        message: "配置获取成功",
        data: config,
        timestamp: new Date().toISOString()
      });

    } else if (req.method === 'POST' || req.method === 'PUT') {
      // 在Vercel环境中，我们只能返回更新后的配置，不能持久化存储
      // 实际的配置更新需要通过重新部署来实现
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const updateData = JSON.parse(body);
          const currentConfig = getConfig();

          if (!currentConfig) {
            return res.status(500).json({
              status: "error",
              message: "无法获取当前配置"
            });
          }

          // 创建更新后的配置（仅用于响应，不会持久化）
          const updatedConfig = JSON.parse(JSON.stringify(currentConfig));

          if (updateData.version_control) {
            updatedConfig.version_control = {
              ...updatedConfig.version_control,
              ...updateData.version_control
            };
          }

          if (updateData.add_version_history) {
            updatedConfig.version_history = updatedConfig.version_history || [];
            updatedConfig.version_history.unshift({
              ...updateData.add_version_history,
              release_date: new Date().toISOString().split('T')[0]
            });
          }

          if (updateData.blocked_versions) {
            updatedConfig.blocked_versions = updateData.blocked_versions;
          }

          if (updateData.admin_notes) {
            updatedConfig.admin_notes = updateData.admin_notes;
          }

          console.log(`[${new Date().toISOString()}] 管理员请求更新配置:`, updateData);
          console.log('注意：在Vercel环境中，配置更改需要通过重新部署来持久化');

          res.json({
            status: "success",
            message: "配置更新请求已处理（注意：需要重新部署以持久化更改）",
            data: updatedConfig,
            timestamp: new Date().toISOString(),
            note: "在Vercel环境中，配置更改需要通过重新部署来持久化"
          });

        } catch (error) {
          console.error('解析请求数据失败:', error);
          res.status(400).json({
            status: "error",
            message: "请求数据格式错误",
            error: error.message
          });
        }
      });
    } else {
      res.status(405).json({
        status: "error",
        message: "不支持的请求方法"
      });
    }
    
  } catch (error) {
    console.error('管理员API错误:', error);
    res.status(500).json({
      status: "error",
      message: "服务器内部错误",
      error: error.message
    });
  }
};
