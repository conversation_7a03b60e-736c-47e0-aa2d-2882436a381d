@echo off
chcp 65001 >nul
echo Cursor Pro 后端进程清理工具
echo ============================

echo 正在查找 Cursor 后端进程...
tasklist | findstr /i "cursor-backend" >nul
if %errorlevel% equ 0 (
    echo 找到以下 Cursor 后端进程:
    tasklist | findstr /i "cursor-backend"
    echo.
    set /p choice="是否要结束这些进程? (Y/N): "
    if /i "%choice%"=="Y" (
        echo 正在结束 Cursor 后端进程...
        taskkill /f /im "cursor-backend.exe" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ 成功结束 Cursor 后端进程
        ) else (
            echo ✗ 结束进程失败，可能需要管理员权限
        )
    ) else (
        echo 取消操作
    )
) else (
    echo 没有找到运行中的 Cursor 后端进程
)

echo.
pause
