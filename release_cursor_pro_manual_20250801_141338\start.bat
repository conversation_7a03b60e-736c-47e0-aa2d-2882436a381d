@echo off
chcp 65001 >nul
title Cursor Pro - 启动器
color 0A

echo ========================================
echo  Cursor Pro - 增强版启动器
echo ========================================
echo.

echo 请选择启动方式:
echo [1] 使用智能启动器 (推荐)
echo [2] 使用标准启动器
echo [3] 直接启动应用
echo [4] 清理后端进程
echo [0] 退出
echo.

set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" (
    echo 启动智能启动器...
    start "" "launcher\Cursor Pro 智能启动器.exe"
) else if "%choice%"=="2" (
    echo 启动标准启动器...
    start "" "launcher\Cursor Pro 启动器.exe"
) else if "%choice%"=="3" (
    echo 直接启动应用...
    start "" "app\cursor_pro\Cursor Pro.exe"
) else if "%choice%"=="4" (
    echo 清理后端进程...
    call "tools\cleanup_cursor_backend.bat"
    pause
    goto :start
) else if "%choice%"=="0" (
    echo 退出程序
    exit /b
) else (
    echo 无效选择，请重新输入
    pause
    goto :start
)

echo.
echo 程序已启动，您可以关闭此窗口
pause
