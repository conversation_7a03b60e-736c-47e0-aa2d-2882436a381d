# 🚀 Cursor Pro 增强工具

> **⚠️ 仅供学习研究使用** - 详见 [免责声明](docs/免责声明.md)

## 🎯 快速启动

### 🖥️ 桌面版（推荐）
```bash
双击运行: start-cursor-pro-desktop.bat
```

### 🌐 网页版
```bash
双击运行: start-cursor-pro-web.bat
```

## 📚 使用文档

- **[📖 完整说明](docs/项目说明.md)** - 详细的项目介绍
- **[🚀 快速上手](docs/快速上手.md)** - 新手入门指南
- **[📚 文档目录](docs/文档目录.md)** - 所有文档索引

## 🔧 环境要求

- **Python 3.7+** - 后端接口服务
- **Node.js 16+** - 前端界面（桌面版需要）

## ✨ 核心功能

- 🔐 Cursor账户管理
- 🛡️ 智能反检测技术
- 🖥️ 桌面应用 + 网页版本
- 💾 SQLite轻量数据库
- ⚡ 高性能优化设计

---

*📝 更多详细信息请查看 [docs](docs/) 文档目录*
