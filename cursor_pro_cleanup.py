#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 进程清理工具
用于清理孤儿后端进程
"""

import os
import sys
import psutil
import subprocess
from pathlib import Path

class CursorProCleanup:
    def __init__(self):
        self.backend_processes = []
        
    def find_cursor_processes(self):
        """查找所有cursor相关进程"""
        cursor_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''
                    proc_exe = proc_info['exe'].lower() if proc_info['exe'] else ''
                    
                    # 查找cursor-backend进程
                    if ('cursor-backend' in proc_name or 
                        'cursor-backend.exe' in proc_exe or
                        (proc_info['cmdline'] and any('cursor-backend' in str(cmd).lower() for cmd in proc_info['cmdline']))):
                        cursor_processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'exe': proc_info['exe']
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            print(f"查找进程时出错: {e}")
            
        return cursor_processes
    
    def kill_process_by_pid(self, pid):
        """根据PID杀死进程"""
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            
            # 等待进程结束
            try:
                proc.wait(timeout=5)
                return True
            except psutil.TimeoutExpired:
                # 如果进程没有在5秒内结束，强制杀死
                proc.kill()
                proc.wait(timeout=3)
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"无法杀死进程 {pid}: {e}")
            return False
        except Exception as e:
            print(f"杀死进程 {pid} 时出错: {e}")
            return False
    
    def cleanup_all_cursor_processes(self):
        """清理所有cursor后端进程"""
        processes = self.find_cursor_processes()
        
        if not processes:
            print("没有找到运行中的 Cursor 后端进程")
            return True
            
        print(f"找到 {len(processes)} 个 Cursor 后端进程:")
        for proc in processes:
            print(f"  PID: {proc['pid']}, 名称: {proc['name']}")
            
        # 询问用户是否要清理
        try:
            response = input("\n是否要清理这些进程? (y/N): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                print("取消清理操作")
                return False
        except KeyboardInterrupt:
            print("\n取消清理操作")
            return False
            
        # 清理进程
        success_count = 0
        for proc in processes:
            print(f"正在清理进程 {proc['pid']} ({proc['name']})...")
            if self.kill_process_by_pid(proc['pid']):
                print(f"  ✓ 成功清理进程 {proc['pid']}")
                success_count += 1
            else:
                print(f"  ✗ 清理进程 {proc['pid']} 失败")
                
        print(f"\n清理完成: {success_count}/{len(processes)} 个进程被成功清理")
        return success_count == len(processes)
    
    def cleanup_silent(self):
        """静默清理所有cursor后端进程"""
        processes = self.find_cursor_processes()
        
        if not processes:
            return True
            
        success_count = 0
        for proc in processes:
            if self.kill_process_by_pid(proc['pid']):
                success_count += 1
                
        return success_count == len(processes)

def main():
    cleanup = CursorProCleanup()
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--silent':
        # 静默模式
        if cleanup.cleanup_silent():
            sys.exit(0)
        else:
            sys.exit(1)
    else:
        # 交互模式
        print("Cursor Pro 进程清理工具")
        print("=" * 30)
        
        try:
            if cleanup.cleanup_all_cursor_processes():
                input("\n按回车键退出...")
            else:
                input("\n清理未完全成功，按回车键退出...")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            sys.exit(0)

if __name__ == "__main__":
    main()
