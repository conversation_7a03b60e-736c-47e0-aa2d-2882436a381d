(['C:\\Users\\<USER>\\Desktop\\cursor-pro\\cursor_pro_launcher_simple.py'],
 ['C:\\Users\\<USER>\\Desktop\\cursor-pro'],
 ['psutil'],
 [('C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\webview\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.4 (tags/v3.13.4:8a526ec, Jun  3 2025, 17:46:04) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('cursor_pro_launcher_simple',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\cursor_pro_launcher_simple.py',
   'PYSOURCE')],
 [('zipfile', 'D:\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\Python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib', 'D:\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\Python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'D:\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\Python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'D:\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'D:\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'D:\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\Python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('struct', 'D:\\Python\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util', 'D:\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\Python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_colorize', 'D:\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ctypes', 'D:\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\Lib\\subprocess.py', 'PYMODULE')],
 [('python313.dll', 'D:\\Python\\python313.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python\\python3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-pro-launcher-simple\\base_library.zip',
   'DATA')],
 [('heapq', 'D:\\Python\\Lib\\heapq.py', 'PYMODULE'),
  ('operator', 'D:\\Python\\Lib\\operator.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Python\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec', 'D:\\Python\\Lib\\encodings\\uu_codec.py', 'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Python\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\Python\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\Python\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Python\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Python\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\Python\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Python\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Python\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\Python\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Python\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Python\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', 'D:\\Python\\Lib\\encodings\\tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Python\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Python\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Python\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\Python\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Python\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Python\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode', 'D:\\Python\\Lib\\encodings\\punycode.py', 'PYMODULE'),
  ('encodings.ptcp154', 'D:\\Python\\Lib\\encodings\\ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', 'D:\\Python\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\Python\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\Python\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Python\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Python\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Python\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Python\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Python\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Python\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Python\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Python\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Python\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Python\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', 'D:\\Python\\Lib\\encodings\\latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', 'D:\\Python\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\Python\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\Python\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\Python\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\Python\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Python\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Python\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Python\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Python\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Python\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Python\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Python\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Python\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Python\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Python\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Python\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Python\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Python\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Python\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Python\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Python\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Python\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\Python\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\Python\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Python\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Python\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\Python\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\Python\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', 'D:\\Python\\Lib\\encodings\\gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', 'D:\\Python\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\Python\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Python\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Python\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\Python\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\Python\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\Python\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\Python\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\Python\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\Python\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\Python\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\Python\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\Python\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\Python\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\Python\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\Python\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\Python\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\Python\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\Python\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\Python\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\Python\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\Python\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\Python\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\Python\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\Python\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\Python\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\Python\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\Python\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\Python\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\Python\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\Python\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\Python\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\Python\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\Python\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\Python\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\Python\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\Python\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\Python\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\Python\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\Python\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\Python\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\Python\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\Python\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\Python\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap', 'D:\\Python\\Lib\\encodings\\charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Python\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Python\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\Python\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Python\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\Python\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases', 'D:\\Python\\Lib\\encodings\\aliases.py', 'PYMODULE'),
  ('encodings', 'D:\\Python\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('re._parser', 'D:\\Python\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\Python\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\Python\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\Python\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\Python\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\Python\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('weakref', 'D:\\Python\\Lib\\weakref.py', 'PYMODULE'),
  ('posixpath', 'D:\\Python\\Lib\\posixpath.py', 'PYMODULE'),
  ('locale', 'D:\\Python\\Lib\\locale.py', 'PYMODULE'),
  ('collections', 'D:\\Python\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Python\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('warnings', 'D:\\Python\\Lib\\warnings.py', 'PYMODULE'),
  ('ntpath', 'D:\\Python\\Lib\\ntpath.py', 'PYMODULE'),
  ('abc', 'D:\\Python\\Lib\\abc.py', 'PYMODULE'),
  ('reprlib', 'D:\\Python\\Lib\\reprlib.py', 'PYMODULE'),
  ('codecs', 'D:\\Python\\Lib\\codecs.py', 'PYMODULE'),
  ('sre_constants', 'D:\\Python\\Lib\\sre_constants.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Python\\Lib\\sre_parse.py', 'PYMODULE'),
  ('traceback', 'D:\\Python\\Lib\\traceback.py', 'PYMODULE'),
  ('io', 'D:\\Python\\Lib\\io.py', 'PYMODULE'),
  ('linecache', 'D:\\Python\\Lib\\linecache.py', 'PYMODULE'),
  ('types', 'D:\\Python\\Lib\\types.py', 'PYMODULE'),
  ('stat', 'D:\\Python\\Lib\\stat.py', 'PYMODULE'),
  ('keyword', 'D:\\Python\\Lib\\keyword.py', 'PYMODULE'),
  ('enum', 'D:\\Python\\Lib\\enum.py', 'PYMODULE'),
  ('genericpath', 'D:\\Python\\Lib\\genericpath.py', 'PYMODULE'),
  ('functools', 'D:\\Python\\Lib\\functools.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Python\\Lib\\sre_compile.py', 'PYMODULE'),
  ('copyreg', 'D:\\Python\\Lib\\copyreg.py', 'PYMODULE'),
  ('os', 'D:\\Python\\Lib\\os.py', 'PYMODULE')])
