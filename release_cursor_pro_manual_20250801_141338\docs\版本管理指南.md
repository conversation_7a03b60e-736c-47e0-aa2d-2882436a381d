# 🚀 版本控制系统使用指南

## 📋 概述

这是一个完整的版本控制系统，让您作为开发者能够：
- 发布新版本
- 强制用户更新
- 禁用旧版本
- 启用维护模式
- 统计用户使用情况

---

## 🔧 开发者操作指南

### **方法1：使用Python脚本（推荐）**

#### 查看当前状态
```bash
python release_manager.py status
```

#### 发布新版本
```bash
# 普通更新
python release_manager.py release 1.1.0 "修复了一些bug"

# 强制更新
python release_manager.py release 1.1.0 "重要安全更新" --force
```

#### 强制所有用户更新
```bash
python release_manager.py force-update 1.1.0
```

#### 维护模式
```bash
# 启用维护模式
python release_manager.py maintenance "系统升级中，预计30分钟完成"

# 禁用维护模式
python release_manager.py maintenance off
```

#### 交互式模式
```bash
python release_manager.py
```

---

### **方法2：直接调用API**

#### 管理员API地址
```
https://cursorpro-api.vercel.app/api/admin
```

#### 认证
在请求头中添加：
```
X-Admin-Key: cursor-admin-2025
```

#### 发布新版本示例
```bash
curl -X POST https://cursorpro-api.vercel.app/api/admin \
  -H "X-Admin-Key: cursor-admin-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "version_control": {
      "latest_version": "1.1.0",
      "min_supported_version": "1.0.0",
      "force_update": false,
      "update_message": "发现新版本 1.1.0，建议更新！"
    },
    "add_version_history": {
      "version": "1.1.0",
      "description": "修复了一些bug，提升了性能"
    }
  }'
```

---

## 📊 版本控制策略

### **1. 普通更新**
- 用户可以选择稍后更新
- 不会阻止程序运行
- 适用于功能改进、小bug修复

### **2. 强制更新**
- 用户必须更新才能继续使用
- 适用于安全更新、重要bug修复
- 设置 `force_update: true`

### **3. 版本禁用**
- 完全禁用特定版本
- 适用于发现严重问题的版本
- 修改 `min_supported_version`

### **4. 维护模式**
- 暂时阻止所有用户访问
- 适用于服务器维护、数据库升级
- 返回503状态码

---

## 🎯 实际使用场景

### **场景1：发布小更新**
```python
# 发布1.0.1版本，用户可选择更新
manager.release_new_version("1.0.1", "修复了界面显示问题", force_update=False)
```

### **场景2：发现安全漏洞**
```python
# 强制所有用户更新到1.0.2
manager.force_update_all("1.0.2")
```

### **场景3：某个版本有严重bug**
```python
# 禁用1.0.1版本，强制用户升级
manager.disable_old_versions(["1.0.1"])
```

### **场景4：服务器维护**
```python
# 启用维护模式
manager.enable_maintenance_mode("服务器升级中，预计1小时完成")

# 维护完成后禁用
manager.disable_maintenance_mode()
```

---

## 📋 配置文件说明

### **version-config.json 结构**
```json
{
  "version_control": {
    "latest_version": "1.0.0",           // 最新版本号
    "min_supported_version": "1.0.0",    // 最低支持版本
    "force_update": false,               // 是否强制更新
    "update_message": "发现新版本...",    // 更新提示消息
    "download_message": "请到群里下载",   // 下载提示
    "download_url": "下载链接",          // 下载地址
    "maintenance_mode": false,           // 维护模式
    "maintenance_message": "维护中..."   // 维护提示
  },
  "version_history": [...],              // 版本历史
  "blocked_versions": [...],             // 被禁用的版本
  "admin_notes": "管理员备注"            // 管理员备注
}
```

---

## 🔒 安全注意事项

1. **管理员密钥**：默认为 `cursor-admin-2025`，建议在Vercel环境变量中设置
2. **API访问**：只有知道管理员密钥的人才能修改配置
3. **备份配置**：重要更改前建议备份配置文件
4. **测试环境**：建议先在测试环境验证更新策略

---

## 🚀 部署更新

每次修改配置后，需要：
1. 提交代码到GitHub
2. 等待Vercel自动部署
3. 验证API是否正常工作

```bash
git add .
git commit -m "Update version control configuration"
git push origin main
```

---

## 📞 用户端体验

用户运行程序时会：
1. 自动检查版本
2. 如果有更新，显示更新对话框
3. 根据配置决定是否强制更新
4. 提供下载链接和说明

---

**现在您就可以完全控制用户的版本更新了！** 🎉
