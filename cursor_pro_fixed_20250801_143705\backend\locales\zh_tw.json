{"menu": {"title": "可用選項", "exit": "退出程式", "reset": "重置機器ID", "register": "註冊新的Cursor帳戶", "register_google": "使用自己的Google帳戶註冊", "register_github": "使用自己的GitHub帳戶註冊", "register_manual": "使用自定義郵箱註冊Cursor", "quit": "關閉Cursor應用", "select_language": "更改語言", "select_chrome_profile": "選擇Chrome配置檔案", "input_choice": "請輸入您的選擇 ({choices})", "invalid_choice": "選擇無效，請輸入 {choices} 範圍內的數字", "program_terminated": "程式已被使用者終止", "error_occurred": "發生錯誤：{error}，請重試", "press_enter": "按返回鍵退出", "disable_auto_update": "禁用 Cursor 自動更新", "lifetime_access_enabled": "終身訪問已啟用", "totally_reset": "完全重置 Cursor", "outdate": "過時", "temp_github_register": "臨時GitHub註冊", "admin_required": "運行可執行文件，需要管理員權限", "admin_required_continue": "繼續使用當前版本...", "coming_soon": "即將推出", "fixed_soon": "即將修復", "contribute": "貢獻項目", "config": "顯示配置", "delete_google_account": "刪除 Cursor Google 帳號", "continue_prompt": "繼續？(y/N): ", "operation_cancelled_by_user": "操作被使用者取消", "exiting": "退出中 ……", "bypass_version_check": "繞過 Cursor 版本檢查", "check_user_authorized": "檢查用戶授權", "bypass_token_limit": "繞過 Token 限制", "language_config_saved": "語言配置保存成功", "lang_invalid_choice": "選擇無效。請輸入以下選項之一：({lang_choices})", "restore_machine_id": "從備份恢復機器ID", "manual_custom_auth": "手動自定義驗證"}, "languages": {"ar": "阿拉伯語", "en": "英文", "zh_cn": "簡體中文", "zh_tw": "繁體中文", "vi": "越南文", "nl": "荷蘭文", "de": "德文", "fr": "法文", "pt": "葡萄牙文", "ru": "俄文", "tr": "土耳其文", "bg": "保加利亞文", "es": "西班牙文", "ja": "日文", "it": "義大利文"}, "quit_cursor": {"start": "開始退出 Cursor", "no_process": "未發現運行中的 Cursor 進程", "terminating": "正在終止進程 {pid}", "waiting": "等待進程退出", "success": "所有 Cursor 進程已正常關閉", "timeout": "以下進程未能在規定時間內關閉: {pids}", "error": "關閉 Cursor 進程時發生錯誤: {error}"}, "reset": {"title": "Cursor 機器標識重置工具", "checking": "檢查配置文件", "not_found": "配置文件未找到", "no_permission": "無法讀取或寫入配置文件，請檢查文件權限", "reading": "讀取當前配置", "creating_backup": "創建配置備份", "backup_exists": "備份文件已存在，跳過備份步驟", "generating": "生成新機器標識", "saving_json": "保存新配置到JSON", "success": "機器標識重置成功", "new_id": "新機器標識", "permission_error": "權限錯誤: {error}", "run_as_admin": "請嘗試以管理員身份運行此程序", "process_error": "重置進程錯誤: {error}", "updating_sqlite": "更新SQLite數據庫", "updating_pair": "更新鍵值對", "sqlite_success": "SQLite數據庫更新成功", "sqlite_error": "SQLite數據庫更新失敗: {error}", "press_enter": "按回車鍵退出", "updating_system_ids": "更新系統ID", "system_ids_updated": "系統ID更新成功", "system_ids_update_failed": "系統ID更新失敗: {error}", "unsupported_os": "不支持的操作系統: {os}", "linux_path_not_found": "Linux路徑未找到", "windows_guid_updated": "Windows GUID更新成功", "windows_permission_denied": "Windows權限拒絕", "windows_guid_update_failed": "Windows GUID更新失敗", "macos_uuid_updated": "macOS UUID更新成功", "plutil_command_failed": "plutil命令失敗", "macos_uuid_update_failed": "macOS UUID更新失敗", "start_patching": "開始修補getMachineId", "current_version": "當前Cursor版本: {version}", "patch_completed": "getMachineId修補完成", "patch_failed": "getMachineId修補失敗: {error}", "version_check_passed": "Cursor版本檢查通過", "file_modified": "文件已修改", "version_less_than_0_45": "Cursor版本 < 0.45.0，跳过getMachineId修补", "detecting_version": "檢測Cursor版本", "patching_getmachineid": "修補getMachineId", "version_greater_than_0_45": "Cursor版本 >= 0.45.0，修補getMachineId", "permission_denied": "權限拒絕: {error}", "backup_created": "備份已創建", "update_success": "更新成功", "update_failed": "更新失敗: {error}", "windows_machine_guid_updated": "Windows機器GUID更新成功", "reading_package_json": "讀取package.json {path}", "invalid_json_object": "JSON對象無效", "no_version_field": "package.json中沒有版本字段", "version_field_empty": "版本字段為空", "invalid_version_format": "版本格式無效: {version}", "found_version": "找到版本: {version}", "version_parse_error": "版本解析錯誤: {error}", "package_not_found": "package.json未找到: {path}", "check_version_failed": "檢查版本失敗: {error}", "stack_trace": "堆疊跟踪", "version_too_low": "Cursor版本太低: {version} < 0.45.0", "no_write_permission": "沒有寫入權限: {path}", "path_not_found": "路徑未找到: {path}", "modify_file_failed": "修改文件失敗: {error}", "windows_machine_id_updated": "Windows機器ID更新成功", "update_windows_machine_id_failed": "更新Windows機器ID失敗: {error}", "update_windows_machine_guid_failed": "更新Windows機器GUID失敗: {error}", "file_not_found": "文件未找到: {path}"}, "register": {"title": "Cursor 註冊工具", "start": "正在啟動註冊流程...", "handling_turnstile": "正在處理安全驗證...", "retry_verification": "正在重試驗證...", "detect_turnstile": "正在檢查安全驗證...", "verification_success": "安全驗證通過", "starting_browser": "正在開啟瀏覽器...", "form_success": "表單提交成功", "browser_started": "瀏覽器已成功開啟", "waiting_for_second_verification": "等待郵箱驗證...", "waiting_for_verification_code": "等待驗證碼...", "password_success": "密碼設定成功", "password_error": "無法設定密碼：{error}，請重試", "waiting_for_page_load": "頁面載入中...", "first_verification_passed": "初始驗證通過", "mailbox": "已成功存取郵箱", "visiting_url": "訪問URL", "register_start": "開始註冊流程", "form_submitted": "表單已提交，開始驗證...", "filling_form": "填寫註冊信息", "basic_info": "基本信息提交完成", "handle_turnstile": "處理 Turnstile 驗證", "no_turnstile": "未檢測到 Turnstile 驗證", "turnstile_passed": "驗證通過", "verification_start": "開始獲取驗證碼", "verification_timeout": "獲取驗證碼超時", "verification_not_found": "未找到驗證碼", "try_get_code": "第 {attempt} 次嘗試獲取驗證碼 | 剩餘時間: {time}秒", "get_account": "獲取帳戶信息", "get_token": "獲取 Cursor Session Token", "token_success": "Token 獲取成功", "token_attempt": "第 {attempt} 次嘗試未獲取到 Token，{time}秒後重試", "token_max_attempts": "已達到最大嘗試次數({max})，獲取 Token 失敗", "token_failed": "獲取 Token 失敗: {error}", "account_error": "獲取帳戶信息失敗: {error}", "email_error": "獲取郵箱地址失敗", "setup_error": "郵箱設置出錯: {error}", "start_getting_verification_code": "開始獲取驗證碼，將在60秒內嘗試...", "get_verification_code_timeout": "獲取驗證碼超時", "get_verification_code_success": "成功獲取驗證碼", "try_get_verification_code": "第 {attempt} 次嘗試未獲取到驗證碼，剩餘時間: {remaining_time}秒", "verification_code_filled": "驗證碼填寫完成", "login_success_and_jump_to_settings_page": "成功登錄並跳轉到設置頁面", "detect_login_page": "檢測到登錄頁面，開始登錄...", "cursor_registration_completed": "註冊完成！", "set_password": "設置密碼", "basic_info_submitted": "基本信息提交完成", "cursor_auth_info_updated": "Cursor 認證信息更新成功", "cursor_auth_info_update_failed": "Cursor 認證信息更新失敗", "reset_machine_id": "重置機器ID", "account_info_saved": "賬戶信息已保存", "save_account_info_failed": "保存賬戶信息失敗", "get_email_address": "獲取郵箱地址", "register_process_error": "註冊流程錯誤: {error}", "update_cursor_auth_info": "更新Cursor認證信息", "setting_password": "設置密碼", "manual_code_input": "手動輸入驗證碼", "manual_email_input": "手動輸入郵箱地址", "suggest_email": "推薦郵箱地址: {suggested_email}", "use_suggested_email_or_enter": "輸入\"yes\"使用此郵箱或直接輸入您想使用的郵箱地址:", "password": "密碼", "first_name": "名字", "last_name": "姓氏", "exit_signal": "退出信號", "email_address": "郵箱地址", "config_created": "配置已創建", "verification_failed": "驗證失敗", "verification_error": "驗證錯誤: {error}", "config_option_added": "配置項已添加: {option}", "config_updated": "配置已更新", "password_submitted": "密碼已提交", "total_usage": "總使用量: {usage}", "setting_on_password": "設置密碼", "getting_code": "正在獲取驗證碼，將在60秒內嘗試...", "using_browser": "使用{瀏覽器}瀏覽器：{path}", "press_enter": "按Enter退出", "try_install_browser": "嘗試使用您的軟件包管理器安裝瀏覽器", "open_mailbox": "打開郵箱頁面", "browser_path_invalid": "{瀏覽器}路徑無效，使用默認路徑", "make_sure_browser_is_properly_installed": "確保正確安裝{瀏覽器}", "using_browser_profile": "使用{browser} profile來自：{user_data_dir}", "no_new_processes_detected": "未檢測到跟踪的新{瀏覽器}進程", "browser_start": "啟動瀏覽器", "max_retries_reached": "達到了最大的重試嘗試。註冊失敗。", "could_not_track_processes": "無法跟踪{瀏覽器}進程：{error}", "human_verify_error": "無法驗證用戶是人類。重試...", "tracking_processes": "跟踪{count} {瀏覽器}進程", "using_tempmail_plus": "使用TempMailPlus進行郵箱驗證", "tempmail_plus_enabled": "TempMailPlus已啟用", "tempmail_plus_disabled": "TempMailPlus已禁用", "tempmail_plus_config_missing": "TempMailPlus配置缺失", "tempmail_plus_email_missing": "未配置TempMailPlus郵箱", "tempmail_plus_epin_missing": "未配置TempMailPlus epin", "tempmail_plus_initialized": "TempMailPlus初始化成功", "tempmail_plus_init_failed": "TempMailPlus初始化失敗：{error}", "tempmail_plus_verification_started": "開始TempMailPlus驗證流程", "tempmail_plus_verification_completed": "TempMailPlus驗證成功完成", "tempmail_plus_verification_failed": "TempMailPlus驗證失敗：{error}"}, "auth": {"title": "Cursor 認證管理器", "checking_auth": "檢查認證文件", "auth_not_found": "未找到認證文件", "auth_file_error": "認證文件錯誤: {error}", "reading_auth": "讀取認證文件", "updating_auth": "更新認證信息", "auth_updated": "認證信息更新成功", "auth_update_failed": "認證信息更新失敗: {error}", "auth_file_created": "認證文件已創建", "auth_file_create_failed": "認證文件創建失敗: {error}", "press_enter": "按回車鍵退出", "connected_to_database": "已連接到數據庫", "database_updated_successfully": "數據庫更新成功", "database_connection_closed": "數據庫連接已關閉", "updating_pair": "更新鍵值對", "db_not_found": "未找到數據庫文件：{path}", "db_permission_error": "無法訪問數據庫文件，請檢查權限", "db_connection_error": "連接數據庫失敗：{error}", "reset_machine_id": "重置機ID"}, "control": {"generate_email": "生成新郵箱", "select_domain": "選擇隨機域名", "copy_email": "複製郵箱地址", "enter_mailbox": "進入郵箱", "refresh_mailbox": "刷新郵箱", "check_verification": "檢查驗證碼", "verification_found": "找到驗證碼", "verification_not_found": "未找到驗證碼", "browser_error": "瀏覽器控制錯誤: {error}", "navigation_error": "導航錯誤: {error}", "email_copy_error": "郵箱複製錯誤: {error}", "mailbox_error": "郵箱錯誤: {error}", "token_saved_to_file": "Token已保存到 cursor_tokens.txt", "navigate_to": "導航到 {url}", "generate_email_success": "生成郵箱成功", "select_email_domain": "選擇郵箱域名", "select_email_domain_success": "選擇郵箱域名成功", "get_email_name": "獲取郵箱名稱", "get_email_name_success": "獲取郵箱名稱成功", "get_email_address": "獲取郵箱地址", "get_email_address_success": "獲取郵箱地址成功", "enter_mailbox_success": "進入郵箱成功", "found_verification_code": "找到驗證碼", "get_cursor_session_token": "獲取Cursor Session Token", "get_cursor_session_token_success": "獲取Cursor Session Token成功", "get_cursor_session_token_failed": "獲取Cursor Session Token失敗", "save_token_failed": "保存Token失敗", "blocked_domain": "被屏蔽的域名", "no_valid_verification_code": "沒有有效的驗證碼", "database_updated_successfully": "數據庫成功更新", "database_connection_closed": "數據庫連接關閉"}, "email": {"starting_browser": "啟動瀏覽器", "visiting_site": "訪問 郵箱網站", "create_success": "郵箱創建成功", "create_failed": "郵箱創建失敗", "create_error": "郵箱創建錯誤: {error}", "refreshing": "刷新郵箱", "refresh_success": "郵箱刷新成功", "refresh_error": "郵箱刷新錯誤: {error}", "refresh_button_not_found": "未找到刷新按鈕", "verification_found": "找到驗證碼", "verification_not_found": "未找到驗證碼", "verification_error": "驗證錯誤: {error}", "verification_code_found": "找到驗證碼", "verification_code_not_found": "未找到驗證碼", "verification_code_error": "驗證碼錯誤: {error}", "address": "郵箱地址", "all_domains_blocked": "所有域名都被屏蔽了，切換服務", "no_available_domains_after_filtering": "過濾後沒有可用域名", "switching_service": "切換到 {service} 服務", "domains_list_error": "獲取域名列表失敗: {error}", "failed_to_get_available_domains": "獲取可用域名失敗", "domains_excluded": "排除的域名: {domains}", "failed_to_create_account": "創建帳戶失敗", "account_creation_error": "帳戶創建錯誤: {error}", "blocked_domains": "被屏蔽的域名: {domains}", "blocked_domains_loaded": "加載被屏蔽的域名: {domains}", "blocked_domains_loaded_error": "加載被屏蔽的域名失敗: {error}", "blocked_domains_loaded_success": "加載被屏蔽的域名成功", "blocked_domains_loaded_timeout": "加載被屏蔽的域名超時: {timeout}秒", "blocked_domains_loaded_timeout_error": "加載被屏蔽的域名超時錯誤: {error}", "available_domains_loaded": "獲取到 {count} 個可用域名", "domains_filtered": "過濾後剩餘 {count} 個可用域名", "trying_to_create_email": "嘗試創建郵箱: {email}", "domain_blocked": "域名被屏蔽: {domain}", "using_chrome_profile": "使用 Chrome 配置文件: {user_data_dir}", "no_display_found": "未找到顯示器。確保 X 伺服器正在運行。", "try_export_display": "嘗試: export DISPLAY=:0", "extension_load_error": "加載插件失敗: {error}", "make_sure_chrome_chromium_is_properly_installed": "確保 Chrome/Chromium 已正確安裝", "try_install_chromium": "嘗試: sudo apt install chromium-browser"}, "update": {"title": "禁用 Cursor 自动更新", "disable_success": "自動更新禁用成功", "disable_failed": "禁用自動更新失敗: {error}", "press_enter": "按回車鍵退出", "start_disable": "開始禁用自動更新", "killing_processes": "殺死進程", "processes_killed": "進程已殺死", "removing_directory": "刪除目錄", "directory_removed": "目錄已刪除", "creating_block_file": "創建阻止文件", "block_file_created": "阻止文件已創建", "clearing_update_yml": "清空 update.yml 文件", "update_yml_cleared": "update.yml 文件已清空", "update_yml_not_found": "update.yml 文件未找到", "clear_update_yml_failed": "清空 update.yml 文件失败: {error}", "unsupported_os": "不支持的操作系统: {system}", "remove_directory_failed": "刪除目錄失败: {error}", "create_block_file_failed": "創建阻止文件失败: {error}", "directory_locked": "目錄被鎖定: {path}", "yml_locked": "update.yml 文件被鎖定", "block_file_locked": "阻止文件被鎖定", "yml_already_locked": "update.yml 文件已鎖定", "block_file_already_locked": "阻止文件已鎖定", "block_file_locked_error": "阻止文件锁定错误: {error}", "yml_locked_error": "update.yml 文件锁定错误: {error}", "block_file_already_locked_error": "阻止文件已锁定错误: {error}", "yml_already_locked_error": "update.yml 文件已锁定错误: {error}"}, "updater": {"checking": "檢查更新...", "new_version_available": "有新版本可用! (當前版本: {current}, 最新版本: {latest})", "updating": "正在更新到最新版本。程序將自動重啟。", "up_to_date": "您使用的是最新版本。", "check_failed": "檢查更新失敗: {error}", "continue_anyway": "繼續使用當前版本...", "update_confirm": "是否要更新到最新版本? (Y/n)", "update_skipped": "跳過更新。", "invalid_choice": "選擇無效。請輸入 'Y' 或 'n'.", "development_version": "開發版本 {current} > {latest}", "changelog_title": "更新日誌", "rate_limit_exceeded": "GitHub API 速率限制超過。跳過更新檢查。"}, "totally_reset": {"title": "完全重置 Cursor", "checking_config": "正在檢查配置檔案", "config_not_found": "找不到配置檔案", "no_permission": "無法讀取或寫入配置檔案，請檢查檔案權限", "reading_config": "正在讀取當前配置", "creating_backup": "正在建立配置備份", "backup_exists": "備份檔案已存在，跳過備份步驟", "generating_new_machine_id": "正在生成新的機器 ID", "saving_new_config": "正在將新配置保存到 JSON", "success": "Cursor 重置成功", "error": "Cursor 重置失敗：{error}", "press_enter": "按 Enter 鍵退出", "reset_machine_id": "重置機器 ID", "database_connection_closed": "資料庫連線已關閉", "database_updated_successfully": "資料庫更新成功", "connected_to_database": "已連接到資料庫", "updating_pair": "正在更新鍵值對", "db_not_found": "未找到資料庫檔案，路徑：{path}", "db_permission_error": "無法訪問資料庫檔案，請檢查權限", "db_connection_error": "連接資料庫失敗：{error}", "feature_title": "功能特色", "feature_1": "完全移除 Cursor AI 設定與配置", "feature_2": "清除所有快取資料，包括 AI 歷史與提示", "feature_3": "重置機器 ID 以繞過試用偵測", "feature_4": "建立新的隨機機器標識", "feature_5": "移除自訂擴充功能與偏好設定", "feature_6": "重置試用資訊與啟動資料", "feature_7": "深度掃描隱藏的授權與試用相關檔案", "feature_8": "安全保留非 Cursor 相關檔案與應用程式", "feature_9": "相容於 Windows、macOS 與 Linux", "disclaimer_title": "免責聲明", "disclaimer_1": "此工具將永久刪除所有 Cursor AI 設定、", "disclaimer_2": "配置與快取資料。此操作無法還原。", "disclaimer_3": "您的程式碼檔案將 **不會** 受到影響，且此工具僅針對", "disclaimer_4": "Cursor AI 編輯器檔案與試用偵測機制。", "disclaimer_5": "系統中的其他應用程式不會受到影響。", "disclaimer_6": "執行此工具後，您需要重新設定 Cursor AI。", "disclaimer_7": "請自行承擔風險", "confirm_title": "您確定要繼續嗎？", "confirm_1": "此操作將刪除所有 Cursor AI 設定、", "confirm_2": "配置與快取資料。此操作無法還原。", "confirm_3": "您的程式碼檔案將 **不會** 受到影響，且此工具僅針對", "confirm_4": "Cursor AI 編輯器檔案與試用偵測機制。", "confirm_5": "系統中的其他應用程式不會受到影響。", "confirm_6": "執行此工具後，您需要重新設定 Cursor AI。", "confirm_7": "請自行承擔風險", "invalid_choice": "請輸入 'Y' 或 'n'", "skipped_for_safety": "出於安全考量跳過（與 Cursor 無關）：{path}", "deleted": "已刪除：{path}", "error_deleting": "刪除 {path} 時出錯：{error}", "not_found": "未找到檔案：{path}", "resetting_machine_id": "正在重置機器 ID 以繞過試用偵測...", "created_machine_id": "已建立新的機器 ID：{path}", "error_creating_machine_id": "建立機器 ID 檔案 {path} 時出錯：{error}", "error_searching": "在 {path} 搜尋檔案時出錯：{error}", "created_extended_trial_info": "已建立新的擴展試用資訊：{path}", "error_creating_trial_info": "建立試用資訊檔案 {path} 時出錯：{error}", "resetting_cursor_ai_editor": "正在重置 Cursor AI 編輯器... 請稍候。", "reset_cancelled": "重置已取消，未進行任何更改。", "windows_machine_id_modification_skipped": "跳過 Windows 機器 ID 修改：{error}", "linux_machine_id_modification_skipped": "跳過 Linux machine-id 修改：{error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "注意：完整的機器 ID 重置可能需要以管理員身份執行", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "注意：完整的系統 machine-id 重置可能需要 sudo 權限", "windows_registry_instructions": "📝 注意：在 Windows 上進行完整重置，您可能還需要清理登錄檔項目。", "windows_registry_instructions_2": "   執行 'regedit' 並搜尋 HKEY_CURRENT_USER\\Software\\ 下包含 'Cursor' 或 'CursorAI' 的鍵並刪除它們。\n", "reset_log_1": "Cursor AI 已完全重置並繞過試用偵測！", "reset_log_2": "請重新啟動系統以使更改生效。", "reset_log_3": "您需要重新安裝 Cursor AI，現在應該有全新的試用期。", "reset_log_4": "為獲得最佳效果，建議還可以：", "reset_log_5": "註冊新試用時使用不同的電子郵件地址", "reset_log_6": "若有可能，使用 VPN 變更 IP 地址", "reset_log_7": "在造訪 Cursor AI 網站前清除瀏覽器的 Cookie 與快取", "reset_log_8": "若仍有問題，嘗試將 Cursor AI 安裝到不同位置", "reset_log_9": "若遇到任何問題，請到 Github Issue Tracker 提交問題：https://github.com/yeongpin/cursor-pro/issues", "unexpected_error": "發生非預期錯誤：{error}", "report_issue": "請在 Github Issue Tracker 回報此問題：https://github.com/yeongpin/cursor-pro/issues", "keyboard_interrupt": "使用者中斷流程，正在退出...", "return_to_main_menu": "返回主選單...", "process_interrupted": "流程已中斷，正在退出...", "press_enter_to_return_to_main_menu": "按 Enter 鍵返回主選單...", "removing_known": "正在移除已知的試用/授權檔案", "performing_deep_scan": "正在進行深度掃描以查找其他試用/授權檔案", "found_additional_potential_license_trial_files": "找到 {count} 個其他潛在試用/授權檔案", "checking_for_electron_localstorage_files": "正在檢查 Electron localStorage 檔案", "no_additional_license_trial_files_found_in_deep_scan": "深度掃描中未發現其他試用/授權檔案", "removing_electron_localstorage_files": "正在移除 Electron localStorage 檔案", "electron_localstorage_files_removed": "已移除 Electron localStorage 檔案", "electron_localstorage_files_removal_error": "移除 Electron localStorage 檔案時出錯：{error}", "removing_electron_localstorage_files_completed": "Electron localStorage 檔案移除完成", "warning_title": "警告", "warning_1": "此操作將刪除所有 Cursor AI 設定、", "warning_2": "配置與快取資料。此操作無法還原。", "warning_3": "您的程式碼檔案將 **不會** 受到影響，且此工具僅針對", "warning_4": "Cursor AI 編輯器檔案與試用偵測機制。", "warning_5": "系統中的其他應用程式不會受到影響。", "warning_6": "執行此工具後，您需要重新設定 Cursor AI。", "warning_7": "請自行承擔風險", "removed": "已刪除：{path}", "failed_to_reset_machine_guid": "無法重置機器 GUID", "failed_to_remove": "無法刪除：{path}", "failed_to_delete_file": "無法刪除檔案：{path}", "failed_to_delete_directory": "無法刪除目錄：{path}", "failed_to_delete_file_or_directory": "無法刪除檔案或目錄：{path}", "deep_scanning": "正在進行深度掃描以查找其他試用/授權檔案", "resetting_cursor": "正在重置 Cursor AI 編輯器... 請稍候。", "completed_in": "完成時間：{time} 秒", "cursor_reset_completed": "Cursor AI 編輯器已完全重置且繞過試用偵測！", "cursor_reset_failed": "Cursor AI 編輯器重置失敗：{error}", "cursor_reset_cancelled": "Cursor AI 編輯器重置已取消，未進行任何更改。", "operation_cancelled": "操作已取消，未進行任何更改。", "delete_button_clicked": "單擊刪除帳戶按鈕", "advanced_tab_clicked": "單擊高級選項卡", "found_danger_zone": "發現的危險區域部分", "advanced_tab_not_found": "多次嘗試後找不到高級標籤", "delete_input_not_found": "多次嘗試後找不到刪除確認輸入", "navigating_to_settings": "導航到設置頁面...", "advanced_tab_error": "錯誤查找高級選項卡：{錯誤}", "delete_input_retry": "刪除未找到輸入，嘗試{嘗試}/{max_attempts}", "direct_advanced_navigation": "嘗試直接導航到高級選項卡", "advanced_tab_retry": "找不到高級選項卡，嘗試{嘗試}/{max_attempts}", "already_on_settings": "已經在設置頁面上", "delete_input_error": "錯誤查找刪除輸入：{error}", "delete_button_retry": "找不到刪除按鈕，嘗試{嘗試}/{max_attempts}", "delete_button_not_found": "多次嘗試後找不到帳戶按鈕", "delete_button_error": "錯誤查找刪除按鈕：{錯誤}", "delete_input_not_found_continuing": "找不到刪除確認輸入，試圖繼續繼續", "login_redirect_failed": "登錄重定向失敗，嘗試直接導航..."}, "github_register": {"title": "GitHub + Cursor AI 注册自动化", "features_header": "功能", "feature1": "使用 1secmail 生成临时邮箱", "feature2": "使用随机凭证注册新的 GitHub 账户", "feature3": "自动验证 GitHub 邮箱", "feature4": "使用 GitHub 认证登录 Cursor AI", "feature5": "重置机器 ID 以绕过试用检测", "feature6": "保存所有凭证到文件", "warnings_header": "警告", "warning1": "此脚本自动化账户创建，可能违反 GitHub/Cursor 服务条款", "warning2": "需要互联网访问和管理员权限", "warning3": "CAPTCHA 或额外验证可能会中断自动化", "warning4": "请负责任地使用，风险自负", "confirm": "您确定要继续吗？", "invalid_choice": "无效选择。请输入 'yes' 或 'no'", "cancelled": "操作已取消", "program_terminated": "程序已由用户终止", "starting_automation": "開始自動化...", "github_username": "GitHub 用戶名", "github_password": "GitHub 密碼", "email_address": "郵箱地址", "credentials_saved": "這些憑證已保存到 github_cursor_accounts.txt", "completed_successfully": "GitHub + Cursor 註冊成功", "registration_encountered_issues": "GitHub + Cursor 註冊遇到問題", "check_browser_windows_for_manual_intervention_or_try_again_later": "檢查瀏覽器視窗進行手動干預或稍後再試"}, "account_info": {"subscription": "訂閱", "trial_remaining": "剩餘試用", "days": "天", "subscription_not_found": "訂閱信息未找到", "email_not_found": "郵箱未找到", "failed_to_get_account": "獲取帳戶信息失敗", "config_not_found": "配置未找到。", "failed_to_get_usage": "獲取使用信息失敗", "failed_to_get_subscription": "獲取訂閱信息失敗", "failed_to_get_email": "獲取郵箱地址失敗", "failed_to_get_token": "獲取 token 失敗", "failed_to_get_account_info": "獲取帳戶信息失敗", "title": "帳戶信息", "email": "郵箱", "token": "Token", "usage": "使用量", "subscription_type": "訂閱類型", "remaining_trial": "剩餘試用", "days_remaining": "剩餘天數", "premium": "高級", "pro": "專業", "pro_trial": "專業試用", "team": "團隊", "enterprise": "企業", "free": "免費", "active": "活躍", "inactive": "非活躍", "premium_usage": "高級使用量", "basic_usage": "基礎使用量", "usage_not_found": "使用量未找到", "lifetime_access_enabled": "永久訪問已啟用", "token_not_found": "Token 未找到"}, "config": {"config_not_available": "配置未找到。", "configuration": "配置", "enabled": "已啟用", "disabled": "已禁用", "config_directory": "配置目錄", "neither_cursor_nor_cursor_directory_found": "未找到 Cursor 或 Cursor 目錄", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "請確保 Cursor 已安裝並至少運行一次", "storage_directory_not_found": "未找到儲存目錄", "storage_file_found": "找到儲存文件", "file_size": "文件大小", "file_permissions": "文件權限", "file_owner": "文件所有者", "file_group": "文件組", "error_getting_file_stats": "獲取文件統計信息時出錯", "permission_denied": "權限拒絕", "try_running": "嘗試運行: {command}", "and": "和", "storage_file_is_empty": "儲存文件為空", "the_file_might_be_corrupted_please_reinstall_cursor": "文件可能已損壞，請重新安裝 Cursor", "storage_file_not_found": "未找到儲存文件", "error_checking_linux_paths": "檢查 Linux 路徑時出錯", "config_option_added": "添加配置選項", "config_updated": "配置更新", "config_created": "配置已創建", "config_setup_error": "配置設置錯誤", "storage_file_is_valid_and_contains_data": "儲存文件有效且包含數據", "error_reading_storage_file": "讀取儲存文件時出錯", "also_checked": "也檢查了 {path}", "backup_created": "備份已創建: {path}", "config_removed": "配置文件已刪除用於強制更新", "backup_failed": "備份失敗: {error}", "force_update_failed": "強制更新配置失敗: {error}", "config_force_update_disabled": "配置文件強制更新已禁用，跳過強制更新", "config_force_update_enabled": "配置文件強制更新已啟用，正在執行強制更新", "documents_path_not_found": "使用當前目錄找不到文檔路徑", "config_dir_created": "配置目錄創建：{path}", "using_temp_dir": "由於錯誤而使用臨時目錄：{path}（錯誤：{error}）"}, "oauth": {"authentication_button_not_found": "未找到認證按鈕", "authentication_failed": "認證失敗: {error}", "found_cookies": "找到 {count} 個 <PERSON>ie", "token_extraction_error": "Token 提取錯誤: {error}", "authentication_successful": "認證成功 - 郵箱: {email}", "missing_authentication_data": "缺少認證數據: {data}", "failed_to_delete_account": "刪除帳戶失敗: {error}", "invalid_authentication_type": "無效的認證類型", "auth_update_success": "認證更新成功", "browser_closed": "瀏覽器已關閉", "auth_update_failed": "認證更新失敗", "google_start": "Google 開始", "github_start": "G<PERSON><PERSON> 開始", "usage_count": "使用量: {usage}", "account_has_reached_maximum_usage": "帳戶已達到最大使用量, {deleting}", "starting_new_authentication_process": "開始新的認證過程...", "failed_to_delete_expired_account": "刪除過期帳戶失敗", "could_not_check_usage_count": "無法檢查使用量: {error}", "found_email": "找到郵箱: {email}", "could_not_find_email": "未找到郵箱: {error}", "could_not_find_usage_count": "未找到使用量: {erro  r}", "already_on_settings_page": "已處於設置頁面", "failed_to_extract_auth_info": "提取認證信息失敗: {error}", "no_chrome_profiles_found": "未找到 Chrome 配置文件, 使用默認配置文件", "found_default_chrome_profile": "找到默認 Chrome 配置文件", "using_first_available_chrome_profile": "使用第一個可用的 Chrome 配置文件: {profile}", "error_finding_chrome_profile": "找不到 Chrome 配置文件, 使用默認配置文件: {error}", "initializing_browser_setup": "初始化瀏覽器設置...", "detected_platform": "檢測平台: {platform}", "running_as_root_warning": "以 root 運行不推薦用於瀏覽器自動化", "consider_running_without_sudo": "考慮不使用 sudo 運行腳本", "no_compatible_browser_found": "未找到兼容的瀏覽器。請安裝 Google Chrome 或 Chromium。", "supported_browsers": "支持的瀏覽器: {platform}", "using_browser_profile": "使用瀏覽器配置文件: {profile}", "starting_browser": "正在啟動瀏覽器: {path}", "browser_setup_completed": "瀏覽器設置完成成功", "browser_setup_failed": "瀏覽器設置失敗: {error}", "try_running_without_sudo_admin": "嘗試不使用 sudo/管理員權限運行", "redirecting_to_authenticator_cursor_sh": "重定向到 authenticator.cursor.sh...", "starting_github_authentication": "開始 Github 認證...", "waiting_for_authentication": "等待認證...", "page_changed_checking_auth": "頁面改變, 檢查認證...", "status_check_error": "狀態檢查錯誤: {error}", "authentication_timeout": "認證超時", "account_is_still_valid": "帳戶仍然有效 (使用量: {usage})", "starting_re_authentication_process": "開始重新認證過程...", "starting_new_google_authentication": "開始新的 Google 認證...", "failed_to_delete_account_or_re_authenticate": "刪除帳戶或重新認證失敗: {error}", "navigating_to_authentication_page": "正在導航到認證頁面...", "please_select_your_google_account_to_continue": "請選擇您的 Google 帳戶以繼續...", "found_browser_data_directory": "找到瀏覽器數據目錄: {path}", "authentication_successful_getting_account_info": "認證成功, 獲取帳戶信息...", "warning_could_not_kill_existing_browser_processes": "警告: 無法殺死現有瀏覽器進程: {error}", "browser_failed_to_start": "瀏覽器啟動失敗: {error}", "browser_failed": "瀏覽器啟動失敗: {error}", "browser_failed_to_start_fallback": "瀏覽器啟動失敗: {error}", "using_configured_browser_path": "使用配置的 {browser} 路徑: {path}", "found_browser_user_data_dir": "找到 {browser} 用戶數據目錄: {path}", "warning_browser_close": "警告：這將關閉所有正在執行的 {browser} 進程", "killing_browser_processes": "正在關閉 {browser} 進程...", "profile_selection_error": "配置文件選擇過程中出錯: {error}", "select_profile": "選擇要使用的 {browser} 配置文件：", "profile_list": "可用 {browser} 配置文件：", "no_profiles": "未找到 {browser} 配置文件", "error_loading": "載入 {browser} 配置文件時出錯：{error}", "profile_selected": "已選擇配置文件：{profile}", "invalid_selection": "選擇無效。請重試", "starting_google_authentication": "開始Google身份驗證...", "found_chrome_at": "在：{path}中找到鉻", "browser_not_found_trying_chrome": "找不到{瀏覽器}，而是嘗試chrome", "error_getting_user_data_directory": "錯誤獲取用戶數據目錄：{error}", "user_data_dir_not_found": "{瀏覽器}用戶數據目錄在{path}上找不到，而是嘗試Chrome"}, "chrome_profile": {"title": "Chrome配置檔案選擇", "select_profile": "選擇要使用的Chrome配置檔案：", "profile_list": "可用配置檔案：", "default_profile": "預設配置檔案", "profile": "配置檔案 {number}", "no_profiles": "未找到Chrome配置檔案", "error_loading": "載入Chrome配置檔案時出錯：{error}", "profile_selected": "已選擇配置檔案：{profile}", "invalid_selection": "選擇無效。請重試", "warning_chrome_close": "警告：這將關閉所有正在執行的Chrome程序"}, "account_delete": {"title": "Cursor Google 帳號刪除工具", "warning": "警告：這將永久刪除您的 Cursor 帳號。此操作無法撤銷。", "cancelled": "帳號刪除已取消。", "starting_process": "開始帳號刪除過程...", "google_button_not_found": "未找到 Google 登錄按鈕", "logging_in": "正在使用 Google 登錄...", "waiting_for_auth": "等待 Google 驗證...", "login_successful": "登錄成功", "unexpected_page": "登錄後頁面異常：{url}", "trying_settings": "嘗試導航到設置頁面...", "select_google_account": "請選擇您的 Google 帳號...", "auth_timeout": "認證超時，繼續執行...", "navigating_to_settings": "正在導航到設置頁面...", "already_on_settings": "已在設置頁面", "login_redirect_failed": "登錄重定向失敗，嘗試直接導航...", "advanced_tab_not_found": "多次嘗試後未找到高級選項卡", "advanced_tab_retry": "未找到高級選項卡，嘗試 {attempt}/{max_attempts}", "advanced_tab_error": "查找高級選項卡時出錯：{error}", "advanced_tab_clicked": "已點擊高級選項卡", "direct_advanced_navigation": "嘗試直接導航到高級選項卡", "delete_button_not_found": "多次嘗試後未找到刪除帳號按鈕", "delete_button_retry": "未找到刪除按鈕，嘗試 {attempt}/{max_attempts}", "delete_button_error": "查找刪除按鈕時出錯：{error}", "delete_button_clicked": "已點擊刪除帳號按鈕", "delete_input_not_found": "多次嘗試後未找到刪除確認輸入框", "delete_input_retry": "未找到刪除輸入框，嘗試 {attempt}/{max_attempts}", "delete_input_error": "查找刪除輸入框時出錯：{error}", "delete_input_not_found_continuing": "未找到刪除確認輸入框，嘗試繼續執行...", "typed_delete": "已在確認框中輸入\"Delete\"", "confirm_button_not_found": "多次嘗試後未找到確認按鈕", "confirm_button_retry": "未找到確認按鈕，嘗試 {attempt}/{max_attempts}", "confirm_button_error": "查找確認按鈕時出錯：{error}", "account_deleted": "帳號刪除成功！", "error": "帳號刪除過程中出錯：{error}", "success": "您的 Cursor 帳號已成功刪除！", "failed": "帳號刪除過程失敗或已取消。", "interrupted": "帳號刪除過程被用戶中斷。", "unexpected_error": "意外錯誤：{error}", "found_email": "找到郵箱：{email}", "email_not_found": "未找到郵箱: {error}", "found_danger_zone": "已找到危險區域部分", "confirm_prompt": "您確定要繼續嗎？(y/N): ", "typed_delete_js": "已使用 JavaScript 輸入\"Delete\""}, "bypass": {"starting": "開始繞過 Cursor 版本限制...", "found_product_json": "找到 product.json: {path}", "no_write_permission": "沒有寫入權限: {path}", "read_failed": "讀取 product.json 失敗: {error}", "current_version": "當前版本: {version}", "backup_created": "備份已創建: {path}", "version_updated": "版本從 {old} 更新到 {new}", "write_failed": "寫入 product.json 失敗: {error}", "no_update_needed": "不需要更新。當前版本 {version} 已 >= 0.46.0", "bypass_failed": "繞過版本限制失敗: {error}", "stack_trace": "堆疊跟踪", "localappdata_not_found": "LOCALAPPDATA 環境變量未找到", "product_json_not_found": "product.json 未在常見 Linux 路徑中找到", "unsupported_os": "不支持的操作系統: {system}", "file_not_found": "文件未找到: {path}", "title": "Cursor 版本繞過工具", "description": "此工具修改 Cursor 的 product.json 以繞過版本限制", "menu_option": "繞過 Cursor 版本檢查"}, "auth_check": {"checking_authorization": "檢查授權...", "token_source": "從資料庫獲取 token 或手動輸入？（d/m, 預設: d）", "getting_token_from_db": "從資料庫獲取 token...", "token_found_in_db": "在資料庫中找到 token", "token_not_found_in_db": "在資料庫中未找到 token", "cursor_acc_info_not_found": "cursor_acc_info.py 未找到", "usage_response_status": "使用情況響應狀態: {response}", "unexpected_status_code": "意外狀態碼: {code}", "jwt_token_warning": "token 似乎是 JWT 格式，但 API 檢查返回意外狀態碼。token 可能有效但 API 訪問受限。", "error_getting_token_from_db": "從資料庫獲取 token 時出錯: {error}", "enter_token": "請輸入您的 Cursor token: ", "token_length": "token 長度: {length}", "invalid_token": "無效的 token", "user_authorized": "用戶已授權", "user_unauthorized": "用戶未授權", "request_timeout": "請求超時", "connection_error": "連接錯誤", "check_error": "檢查授權時出錯: {error}", "authorization_successful": "授權成功", "authorization_failed": "授權失敗", "operation_cancelled": "操作已取消", "unexpected_error": "意外錯誤: {error}", "error_generating_checksum": "生成校驗和時出錯: {error}", "checking_usage_information": "檢查使用情況...", "check_usage_response": "檢查使用情況響應: {response}", "usage_response": "使用情況響應: {response}"}, "bypass_token_limit": {"title": "繞過 Token 限制工具", "description": "此工具修改 workbench.desktop.main.js 文件以繞過 token 限制", "press_enter": "按回車鍵繼續..."}, "restore": {"title": "從備份恢復機器ID", "starting": "正在啟動機器ID恢復進程", "no_backups_found": "未找到備份文件", "available_backups": "可用的備份文件", "select_backup": "選擇要恢復的備份", "to_cancel": "取消操作", "operation_cancelled": "操作已取消", "invalid_selection": "選擇無效", "please_enter_number": "請輸入有效的數字", "missing_id": "缺少ID: {id}", "read_backup_failed": "讀取備份文件失敗: {error}", "current_file_not_found": "未找到當前存儲文件", "current_backup_created": "已創建當前存儲文件的備份", "storage_updated": "存儲文件已成功更新", "update_failed": "更新存儲文件失敗: {error}", "sqlite_not_found": "未找到SQLite數據庫", "updating_sqlite": "正在更新SQLite數據庫", "updating_pair": "正在更新鍵值對", "sqlite_updated": "SQLite數據庫已成功更新", "sqlite_update_failed": "更新SQLite數據庫失敗: {error}", "machine_id_backup_created": "已創建machineId文件的備份", "backup_creation_failed": "創建備份失敗: {error}", "machine_id_updated": "machineId文件已成功更新", "machine_id_update_failed": "更新machineId文件失敗: {error}", "updating_system_ids": "正在更新系統ID", "system_ids_update_failed": "更新系統ID失敗: {error}", "permission_denied": "權限被拒絕。請嘗試以管理員身份運行", "windows_machine_guid_updated": "Windows機器GUID已成功更新", "update_windows_machine_guid_failed": "更新Windows機器GUID失敗: {error}", "windows_machine_id_updated": "Windows機器ID已成功更新", "update_windows_machine_id_failed": "更新Windows機器ID失敗: {error}", "sqm_client_key_not_found": "未找到SQMClient註冊表項", "update_windows_system_ids_failed": "更新Windows系統ID失敗: {error}", "macos_platform_uuid_updated": "macOS平台UUID已成功更新", "failed_to_execute_plutil_command": "執行plutil命令失敗", "update_macos_system_ids_failed": "更新macOS系統ID失敗: {error}", "ids_to_restore": "要恢復的機器ID", "confirm": "您確定要恢復這些ID嗎？", "success": "機器ID已成功恢復", "process_error": "恢復過程錯誤: {error}", "press_enter": "按Enter鍵繼續"}, "browser_profile": {"invalid_selection": "無效的選擇。請重試。", "profile_list": "可用{瀏覽器}配置文件：", "profile_selected": "選定的個人資料：{配置文件}", "error_loading": "錯誤加載{瀏覽器} profiles：{error}", "default_profile": "默認配置文件", "no_profiles": "找不到{瀏覽器}配置文件", "select_profile": "選擇要使用的{瀏覽器}配置文件：", "profile": "個人資料{數字}", "title": "瀏覽器配置文件選擇"}, "token": {"refresh_failed": "令牌刷新失敗：{error}", "refreshing": "刷新的代幣...", "connection_error": "連接錯誤刷新服務器", "refresh_success": "令牌成功刷新了！有效{天}天（到期：{expire}）", "extraction_error": "錯誤提取令牌：{error}", "request_timeout": "要求刷新服務器的要求", "unexpected_error": "令牌刷新期間的意外錯誤：{error}", "server_error": "刷新服務器錯誤：http {status}", "no_access_token": "沒有訪問令牌", "invalid_response": "刷新服務器的JSON響應無效"}, "manual_auth": {"proceed_prompt": "繼續？ （y/n）：", "token_verification_skipped": "跳過令牌驗證（Check_user_authorized.py找不到）", "auth_updated_successfully": "身份驗證信息成功更新了！", "auth_type_google": "Google", "continue_anyway": "無論如何繼續？ （y/n）：", "auth_type_selected": "選定的身份驗證類型：{type}", "auth_type_github": "github", "token_required": "需要令牌", "random_email_generated": "生成的隨機電子郵件：{電子郵件}", "verifying_token": "驗證令牌有效性...", "operation_cancelled": "操作取消了", "error": "錯誤：{錯誤}", "auth_type_prompt": "選擇身份驗證類型：", "email_prompt": "輸入電子郵件（留空白以獲取隨機電子郵件）：", "auth_type_auth0": "auth_0（默認）", "token_verification_error": "錯誤驗證令牌：{error}", "token_verified": "令牌成功驗證了！", "confirm_prompt": "請確認以下信息：", "token_prompt": "輸入光標令牌（access_token/refresh_token）：", "invalid_token": "無效的令牌。身份驗證中止。", "title": "手動Cursor身份驗證", "updating_database": "更新Cursor身份驗證數據庫...", "auth_update_failed": "無法更新身份驗證信息"}, "tempmail": {"general_error": "發生錯誤：{error}", "config_error": "配置文件錯誤：{error}", "no_email": "找不到Cursor驗證電子郵件", "checking_email": "檢查Cursor驗證電子郵件...", "extract_code_failed": "提取驗證代碼失敗：{error}", "configured_email": "配置的電子郵件：{email}", "no_code": "無法獲得驗證代碼", "check_email_failed": "檢查電子郵件失敗：{error}", "email_found": "找到Cursor驗證電子郵件", "verification_code": "驗證代碼：{code}"}}