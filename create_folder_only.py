#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建 Cursor Pro 文件夹版本
只生成文件夹，不创建ZIP
"""

import os
import sys
import shutil
from pathlib import Path

def create_release_directory():
    """创建发布目录"""
    release_dir = Path('Cursor_Pro')
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    print(f"📁 创建发布目录: {release_dir}")
    return release_dir

def copy_core_components(release_dir):
    """复制核心组件"""
    print("\n📦 复制核心组件...")
    
    # 1. 复制主启动器到根目录
    if Path('dist/Cursor Pro 智能启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 智能启动器.exe', release_dir / 'Cursor Pro.exe')
        print("✅ 复制智能启动器为主启动器")
    elif Path('dist/Cursor Pro 启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 启动器.exe', release_dir / 'Cursor Pro.exe')
        print("✅ 复制标准启动器为主启动器")
    
    # 2. 复制前端应用
    if Path('cursor_pro/Cursor Pro.exe').exists():
        frontend_dir = release_dir / 'frontend'
        frontend_dir.mkdir()
        
        # 复制前端主程序
        shutil.copy2('cursor_pro/Cursor Pro.exe', frontend_dir / 'Cursor Pro.exe')
        
        # 复制前端依赖文件
        frontend_files = [
            'chrome_100_percent.pak', 'chrome_200_percent.pak', 'resources.pak',
            'snapshot_blob.bin', 'v8_context_snapshot.bin', 'icudtl.dat',
            'd3dcompiler_47.dll', 'ffmpeg.dll', 'libEGL.dll', 'libGLESv2.dll',
            'vk_swiftshader.dll', 'vulkan-1.dll', 'vk_swiftshader_icd.json',
            'LICENSES.chromium.html'
        ]
        
        for file in frontend_files:
            src_path = Path('cursor_pro') / file
            if src_path.exists():
                shutil.copy2(src_path, frontend_dir / file)
        
        # 复制前端目录
        frontend_dirs = ['locales', 'resources']
        for dir_name in frontend_dirs:
            src_dir = Path('cursor_pro') / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, frontend_dir / dir_name, dirs_exist_ok=True)
        
        print("✅ 复制前端应用和依赖")
    
    # 3. 复制后端服务
    if Path('dist/cursor-backend.exe').exists():
        backend_dir = release_dir / 'backend'
        backend_dir.mkdir()
        
        shutil.copy2('dist/cursor-backend.exe', backend_dir / 'cursor-backend.exe')
        
        # 复制后端依赖
        backend_dirs = ['api', 'database', 'locales', 'images']
        for dir_name in backend_dirs:
            src_dir = Path('dist') / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, backend_dir / dir_name, dirs_exist_ok=True)
        
        # 复制后端配置文件
        backend_files = ['requirements.txt']
        for file in backend_files:
            src_path = Path('dist') / file
            if src_path.exists():
                shutil.copy2(src_path, backend_dir / file)
        
        print("✅ 复制后端服务")
    
    # 4. 复制文档 (可选)
    if Path('docs').exists():
        shutil.copytree('docs', release_dir / 'docs', dirs_exist_ok=True)
        print("✅ 复制文档目录")
    
    # 5. 复制重要文件
    important_files = ['README.md', '快速上手.md']
    for file in important_files:
        if Path(file).exists():
            shutil.copy2(file, release_dir / file)
            print(f"✅ 复制: {file}")

def create_simple_readme(release_dir):
    """创建简单的使用说明"""
    readme_content = '''# Cursor Pro

## 使用方法

双击 Cursor Pro.exe 即可启动

## 目录结构

- Cursor Pro.exe - 主启动器
- frontend/ - 前端应用
- backend/ - 后端服务
- docs/ - 文档

启动器会自动管理前后端进程。
'''
    
    readme_path = release_dir / 'README.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建使用说明: README.txt")

def get_folder_size(folder_path):
    """计算文件夹大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

def main():
    """主函数"""
    print("=" * 40)
    print("📁 Cursor Pro - 文件夹版本")
    print("=" * 40)
    
    # 创建发布目录
    release_dir = create_release_directory()
    
    # 复制核心组件
    copy_core_components(release_dir)
    
    # 创建使用说明
    create_simple_readme(release_dir)
    
    # 计算大小
    folder_size = get_folder_size(release_dir)
    
    print("\n" + "=" * 40)
    print("🎉 文件夹版本创建完成!")
    print("=" * 40)
    print(f"📁 目录: {release_dir}")
    print(f"📊 大小: {format_size(folder_size)}")
    print("\n🚀 使用方法:")
    print("双击 Cursor Pro/Cursor Pro.exe")

if __name__ == "__main__":
    main()
