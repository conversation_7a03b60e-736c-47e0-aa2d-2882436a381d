# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['cursor_pro_mini_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除所有不必要的模块
        'tkinter', 'tkinter.*', '_tkinter',
        'matplotlib', 'numpy', 'scipy', 'pandas',
        'PIL', 'Pillow', 'cv2', 'sklearn',
        'tensorflow', 'torch', 'keras',
        'jupyter', 'IPython', 'notebook',
        'django', 'flask', 'fastapi',
        'requests', 'urllib3', 'certifi',
        'setuptools', 'pip', 'wheel',
        'pytest', 'unittest', 'doctest',
        'pydoc', 'pdb', 'profile', 'cProfile',
        'multiprocessing', 'concurrent.futures',
        'asyncio', 'threading',
        'xml', 'html', 'email', 'http',
        'sqlite3', 'dbm', 'pickle',
        'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile',
        'wave', 'audioop', 'chunk', 'sunau',
        'turtle', 'curses', 'readline',
        'distutils', 'ensurepip',
        'argparse', 'getopt', 'optparse',
        'logging', 'warnings', 'traceback',
        'pprint', 'reprlib', 'copy', 'deepcopy',
        'collections', 'heapq', 'bisect',
        'array', 'weakref', 'types',
        'decimal', 'fractions', 'random',
        'statistics', 'math', 'cmath',
        'itertools', 'functools', 'operator',
        'string', 'textwrap', 're',
        'difflib', 'unicodedata', 'stringprep',
        'calendar', 'datetime', 'zoneinfo',
        'hashlib', 'hmac', 'secrets',
        'base64', 'binascii', 'codecs',
        'json', 'plistlib', 'configparser',
        'netrc', 'xdrlib', 'csv',
    ],
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Cursor Pro 启动器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
