@echo off
chcp 65001 >nul
title Cursor Pro
color 0A

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗ ██████╗ ██████╗     ██████╗ ██████╗  ██████╗ 
echo ██╔════╝██║   ██║██╔══██╗██╔════╝██╔═══██╗██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗
echo ██║     ██║   ██║██████╔╝███████╗██║   ██║██████╔╝    ██████╔╝██████╔╝██║   ██║
echo ██║     ██║   ██║██╔══██╗╚════██║██║   ██║██╔══██╗    ██╔═══╝ ██╔══██╗██║   ██║
echo ╚██████╗╚██████╔╝██║  ██║███████║╚██████╔╝██║  ██║    ██║     ██║  ██║╚██████╔╝
echo  ╚═════╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝    ╚═╝     ╚═╝  ╚═╝ ╚═════╝ 
echo.
echo                           增强版 - 智能启动系统
echo.

echo 🚀 正在启动 Cursor Pro...
echo.

REM 启动主程序
start "" "Cursor Pro.exe"

echo ✅ Cursor Pro 已启动
echo.
echo 💡 提示:
echo    - 如需清理后端进程，运行: tools\清理后端进程.bat
echo    - 查看文档: docs\ 目录
echo.
echo 按任意键关闭此窗口...
pause >nul
