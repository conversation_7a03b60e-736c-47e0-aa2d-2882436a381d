#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建 Cursor Pro 纯净便携版
只打包程序文件，不包含安装脚本
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_release_directory():
    """创建发布目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = Path(f'cursor_pro_portable_{timestamp}')
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    print(f"📁 创建便携版目录: {release_dir}")
    return release_dir

def copy_core_components(release_dir):
    """复制核心组件"""
    print("\n📦 复制核心组件...")
    
    # 复制启动器到根目录
    if Path('dist/Cursor Pro 启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 启动器.exe', release_dir / 'Cursor Pro 启动器.exe')
        print("✅ 复制启动器到根目录")
    
    if Path('dist/Cursor Pro 智能启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 智能启动器.exe', release_dir / 'Cursor Pro 智能启动器.exe')
        print("✅ 复制智能启动器到根目录")
    
    # 复制完整的 cursor_pro 应用
    if Path('cursor_pro').exists():
        shutil.copytree('cursor_pro', release_dir / 'cursor_pro', dirs_exist_ok=True)
        print("✅ 复制 Cursor Pro 应用")
    
    # 复制清理工具
    if Path('cleanup_cursor_backend.bat').exists():
        shutil.copy2('cleanup_cursor_backend.bat', release_dir / 'cleanup_cursor_backend.bat')
        print("✅ 复制清理工具")
    
    # 复制重要文档
    important_files = [
        'README.md',
        '快速上手.md',
        '文档目录.md'
    ]
    
    for file in important_files:
        if Path(file).exists():
            shutil.copy2(file, release_dir / file)
            print(f"✅ 复制: {file}")
    
    # 复制文档目录（可选）
    if Path('docs').exists():
        shutil.copytree('docs', release_dir / 'docs', dirs_exist_ok=True)
        print("✅ 复制文档目录")

def create_simple_readme(release_dir):
    """创建简单的使用说明"""
    readme_content = '''# Cursor Pro - 便携版

## 🚀 快速启动

### 推荐方式
双击 `Cursor Pro 智能启动器.exe` (自动管理前后端进程)

### 其他方式
- `Cursor Pro 启动器.exe` - 标准启动器
- `cursor_pro/Cursor Pro.exe` - 直接启动主程序

## 🔧 故障排除

如果遇到后端进程残留:
双击 `cleanup_cursor_backend.bat` 清理进程

## 📁 目录说明

- `Cursor Pro 智能启动器.exe` - 智能启动器 (推荐)
- `Cursor Pro 启动器.exe` - 标准启动器
- `cursor_pro/` - 主程序目录
- `cleanup_cursor_backend.bat` - 进程清理工具
- `docs/` - 详细文档

## 📖 详细文档

查看 `docs/` 目录或 `快速上手.md` 获取更多信息

---
**Cursor Pro Portable Edition**
'''
    
    readme_path = release_dir / 'README.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建使用说明: README.txt")

def create_zip_package(release_dir):
    """创建 ZIP 压缩包"""
    print("\n🗜️ 创建便携版压缩包...")
    
    zip_name = f'{release_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    return zip_name

def get_folder_size(folder_path):
    """计算文件夹大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

def main():
    """主函数"""
    print("=" * 50)
    print("📦 Cursor Pro - 便携版打包工具")
    print("=" * 50)
    
    # 创建发布目录
    release_dir = create_release_directory()
    
    # 复制核心组件
    copy_core_components(release_dir)
    
    # 创建使用说明
    create_simple_readme(release_dir)
    
    # 计算大小
    folder_size = get_folder_size(release_dir)
    
    # 创建压缩包
    zip_file = create_zip_package(release_dir)
    zip_size = os.path.getsize(zip_file)
    
    print("\n" + "=" * 50)
    print("🎉 便携版打包完成!")
    print("=" * 50)
    print(f"📁 便携版目录: {release_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📊 目录大小: {format_size(folder_size)}")
    print(f"📊 压缩包大小: {format_size(zip_size)}")
    print("\n🚀 使用方法:")
    print("1. 解压 ZIP 文件到任意位置")
    print("2. 双击 'Cursor Pro 智能启动器.exe'")
    print("3. 享受完整功能!")
    print("\n💡 特点:")
    print("- 无需安装，解压即用")
    print("- 智能进程管理")
    print("- 包含完整功能")
    print("- 自带清理工具")

if __name__ == "__main__":
    main()
