# Cursor Pro - Python Dependencies
# 核心依赖包 / Core Dependencies

# 终端颜色输出 / Terminal color output
colorama>=0.4.6

# Web浏览器自动化 / Web browser automation
playwright>=1.40.0
selenium>=4.15.0

# HTTP请求库 / HTTP requests library
requests>=2.31.0

# Web框架 / Web framework
flask>=2.3.0
flask-cors>=4.0.0

# 防检测相关 / Anti-detection related
undetected-chromedriver>=3.5.4
selenium-stealth>=1.0.6
fake-useragent>=1.4.0

# 数据处理 / Data processing
pandas>=2.1.0
openpyxl>=3.1.0

# 系统工具 / System utilities
psutil>=5.9.0
pathlib2>=2.3.7

# 配置文件处理 / Configuration file handling
configparser>=6.0.0
python-dotenv>=1.0.0

# 日期时间处理 / Date and time handling
python-dateutil>=2.8.2

# JSON处理增强 / Enhanced JSON processing
ujson>=5.8.0

# 加密相关 / Cryptography
cryptography>=41.0.0

# 网络工具 / Network utilities
urllib3>=2.0.0

# 开发工具 / Development tools (可选 / Optional)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0

# 注意：SQLite3是Python内置模块，无需安装
# Note: SQLite3 is a built-in Python module, no installation required
