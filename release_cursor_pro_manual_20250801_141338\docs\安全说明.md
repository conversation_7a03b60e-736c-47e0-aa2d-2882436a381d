# 🔒 安全说明 / Security Information

## 🛡️ 软件安全特性

### 数据隐私保护

1. **本地存储**: 所有数据存储在用户本地，不上传到任何服务器
2. **数据隔离**: 每个用户的数据完全独立，互不干扰
3. **无网络传输**: 核心功能无需网络连接，保护隐私安全
4. **开源透明**: 源代码公开，可审查安全性

### 系统安全

1. **无系统修改**: 软件不修改系统关键文件
2. **用户权限**: 仅使用普通用户权限运行
3. **沙箱运行**: 在用户目录下创建独立的工作环境
4. **可逆操作**: 所有操作都可以撤销或恢复

## 🔍 安全验证

### 文件完整性

```bash
# 验证下载文件的完整性
# Windows
certutil -hashfile CursorPro_1.0.0_windows.exe SHA256

# Linux/macOS
sha256sum CursorPro_1.0.0_linux_x64
```

### 病毒扫描建议

建议用户在运行前进行病毒扫描：
- Windows Defender
- Malwarebytes
- VirusTotal在线扫描

### 源码审查

- 📂 **开源项目**: 完整源代码可在GitHub查看
- 🔍 **代码审查**: 欢迎安全专家审查代码
- 📋 **构建过程**: GitHub Actions自动化构建，过程透明

## ⚠️ 安全建议

### 使用前

1. **备份数据**: 使用前备份重要的Cursor配置
2. **创建还原点**: Windows用户建议创建系统还原点
3. **关闭杀毒软件**: 临时关闭可能误报的杀毒软件
4. **网络隔离**: 可在断网环境下使用核心功能

### 使用中

1. **监控行为**: 观察软件运行时的系统行为
2. **权限控制**: 不要给予不必要的管理员权限
3. **定期检查**: 定期检查生成的文件和配置
4. **及时更新**: 使用最新版本以获得安全修复

### 使用后

1. **清理痕迹**: 如需要，可删除配置目录
2. **恢复设置**: 可恢复原始的Cursor配置
3. **监控账户**: 观察Cursor账户是否有异常

## 🚨 风险提示

### 账户风险

- Cursor账户可能被官方检测并封禁
- 可能失去已购买的正版授权
- 影响正常的开发工作流程

### 法律风险

- 可能违反软件使用协议
- 在某些地区可能涉及法律问题
- 商业使用可能面临法律诉讼

### 技术风险

- 软件更新可能导致功能失效
- 可能与系统更新产生冲突
- 数据丢失或配置损坏的风险

## 📞 安全问题报告

如发现安全问题，请通过以下方式报告：

### 联系方式
- **GitHub Issues**: [项目地址]/issues
- **安全邮箱**: security@[项目域名]
- **加密通信**: 支持PGP加密

### 报告内容
1. 问题详细描述
2. 复现步骤
3. 影响范围评估
4. 建议的修复方案

## 🔐 最佳实践

### 开发者

1. **代码审查**: 所有代码变更都经过审查
2. **依赖检查**: 定期检查第三方依赖的安全性
3. **自动化测试**: 包含安全性测试用例
4. **及时响应**: 快速响应安全问题报告

### 用户

1. **谨慎使用**: 充分了解风险后再使用
2. **环境隔离**: 在测试环境中先试用
3. **备份重要数据**: 使用前做好备份
4. **关注更新**: 及时关注安全更新

---

**安全是我们的首要关注点。如有任何安全疑虑，请及时联系我们。**

**Security is our top priority. Please contact us immediately if you have any security concerns.**
