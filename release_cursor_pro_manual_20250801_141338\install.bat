@echo off
chcp 65001 >nul
title Cursor Pro - 安装程序
color 0B

echo ========================================
echo  Cursor Pro - 安装程序
echo ========================================
echo.

echo 此程序将安装 Cursor Pro 到您的系统
echo.

set "INSTALL_DIR=C:\Program Files\Cursor Pro"

echo 安装目录: %INSTALL_DIR%
echo.

set /p confirm="确认安装? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo 安装已取消
    pause
    exit /b
)

echo.
echo 正在安装...

REM 创建安装目录
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

REM 复制文件
echo 复制程序文件...
xcopy /E /I /Y "app\*" "%INSTALL_DIR%\app\"
xcopy /E /I /Y "launcher\*" "%INSTALL_DIR%\launcher\"
xcopy /E /I /Y "tools\*" "%INSTALL_DIR%\tools\"
xcopy /E /I /Y "docs\*" "%INSTALL_DIR%\docs\"

copy /Y "*.md" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.txt" "%INSTALL_DIR%\" >nul 2>&1

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Cursor Pro.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\launcher\Cursor Pro 智能启动器.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo ========================================
echo  安装完成!
echo ========================================
echo.
echo Cursor Pro 已成功安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
echo 使用方法:
echo 1. 双击桌面快捷方式启动
echo 2. 或运行: %INSTALL_DIR%\launcher\Cursor Pro 智能启动器.exe
echo.
pause
