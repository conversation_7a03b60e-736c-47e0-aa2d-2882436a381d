# Cursor Pro - 完整发布包

## 📦 包含内容

### 🚀 启动器
- `launcher/Cursor Pro 智能启动器.exe` - 智能启动器 (推荐)
- `launcher/Cursor Pro 启动器.exe` - 标准启动器

### 🖥️ 主应用
- `app/cursor_pro/` - 完整的 Cursor Pro 应用程序
- `app/cursor_pro/Cursor Pro.exe` - 主程序
- `app/cursor_pro/resources/python-backend/` - 后端服务

### 🛠️ 工具
- `tools/cleanup_cursor_backend.bat` - 后端进程清理工具

### 📚 文档
- `docs/` - 完整文档和指南
- `README.md` - 项目说明
- `快速上手.md` - 快速开始指南

## 🚀 快速开始

### 方法1: 自动安装 (推荐)
1. 双击 `install.bat` 运行安装程序
2. 按照提示完成安装
3. 使用桌面快捷方式启动

### 方法2: 便携运行
1. 双击 `start.bat` 选择启动方式
2. 推荐选择 "智能启动器"

### 方法3: 手动启动
1. 运行 `launcher/Cursor Pro 智能启动器.exe`
2. 或直接运行 `app/cursor_pro/Cursor Pro.exe`

## 🔧 故障排除

如果遇到后端进程残留问题:
1. 运行 `tools/cleanup_cursor_backend.bat`
2. 或使用任务管理器手动结束 `cursor-backend.exe` 进程

## 📖 详细文档

查看 `docs/` 目录获取:
- 完整安装指南
- 配置说明
- 技术支持
- 常见问题解答

## ⚠️ 重要提醒

- 本软件仅供学习和研究使用
- 请阅读相关文档了解使用方法
- 如遇问题请查看技术支持文档

---

**Cursor Pro Enhanced Edition**
*智能启动 · 稳定运行 · 完整功能*
