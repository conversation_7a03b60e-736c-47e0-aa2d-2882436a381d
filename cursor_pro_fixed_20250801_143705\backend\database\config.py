"""
SQLite数据库配置管理 - 纯SQLite版本
"""
import os
import sqlite3
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class SQLiteConfig:
    """SQLite数据库配置类"""

    def __init__(self):
        """初始化SQLite配置"""
        # 获取数据库文件路径
        self.db_path = self._get_default_sqlite_path()

        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        os.makedirs(db_dir, exist_ok=True)

        # SQLite配置
        self.timeout = 30.0
        self.check_same_thread = False
        self.isolation_level = None  # 自动提交模式

    def _get_default_sqlite_path(self):
        """获取默认SQLite数据库路径"""
        try:
            # 优先使用统一的数据路径管理器
            from data_path_manager import get_data_path_manager
            path_manager = get_data_path_manager()
            db_path = str(path_manager.get_data_file('cursor_pro.db'))
            logger.info(f"✅ 使用统一路径管理器: {db_path}")
            return db_path
        except ImportError as e:
            logger.warning(f"⚠️ 数据路径管理器不可用，使用降级路径: {e}")
            # 降级到原来的逻辑（向后兼容）
            if os.name == 'nt':  # Windows
                base_dir = os.path.join(os.path.expanduser('~'), 'Documents', '.cursor-pro')
            else:  # macOS/Linux
                base_dir = os.path.join(os.path.expanduser('~'), '.cursor-pro')

            os.makedirs(base_dir, exist_ok=True)
            return os.path.join(base_dir, 'cursor_pro.db')

class SQLiteManager:
    """SQLite数据库管理器"""

    def __init__(self):
        """初始化数据库管理器"""
        self.config = SQLiteConfig()
        self._initialize_database()

    def _initialize_database(self):
        """初始化数据库"""
        try:
            # 如果数据库文件不存在，创建它
            if not os.path.exists(self.config.db_path):
                self._create_database()

            logger.info(f"✅ SQLite数据库初始化成功: {self.config.db_path}")

        except Exception as e:
            logger.error(f"❌ SQLite数据库初始化失败: {e}")
            raise

    def _create_database(self):
        """创建数据库和表结构"""
        with self.get_connection_context() as conn:
            # 启用外键支持
            conn.execute("PRAGMA foreign_keys = ON")

            # 读取并执行初始化SQL
            init_sql_path = os.path.join(os.path.dirname(__file__), 'sqlite_init.sql')
            if os.path.exists(init_sql_path):
                with open(init_sql_path, 'r', encoding='utf-8') as f:
                    sql_content = f.read()

                # 执行SQL语句
                conn.executescript(sql_content)
                conn.commit()
                logger.info("✅ SQLite数据库表结构创建成功")
            else:
                # 如果没有初始化SQL文件，创建基本表结构
                self._create_basic_tables(conn)

    def _create_basic_tables(self, conn):
        """创建基本表结构"""
        # 用户表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT,
                device_id TEXT UNIQUE,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                last_login TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'banned'))
            )
        """)

        # 认证信息表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cursor_auth (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                auth_key TEXT NOT NULL,
                auth_value TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE(user_id, auth_key)
            )
        """)

        # 账户表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cursor_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                email TEXT NOT NULL,
                access_token TEXT,
                refresh_token TEXT,
                auth_type TEXT DEFAULT 'Auth_0',
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)

        # 插入默认用户
        conn.execute("""
            INSERT OR IGNORE INTO users (id, email, device_id, status)
            VALUES (1, '<EMAIL>', 'local-device', 'active')
        """)

        conn.commit()
        logger.info("✅ 基本表结构创建成功")

    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = sqlite3.connect(
                self.config.db_path,
                timeout=self.config.timeout,
                check_same_thread=self.config.check_same_thread,
                isolation_level=self.config.isolation_level
            )

            # 启用外键支持
            conn.execute("PRAGMA foreign_keys = ON")

            # 设置WAL模式以提高并发性能
            conn.execute("PRAGMA journal_mode = WAL")

            # 设置同步模式
            conn.execute("PRAGMA synchronous = NORMAL")

            return conn

        except Exception as e:
            logger.error(f"❌ 获取数据库连接失败: {e}")
            raise

    @contextmanager
    def get_connection_context(self):
        """获取数据库连接的上下文管理器"""
        conn = self.get_connection()
        try:
            yield conn
        finally:
            conn.close()

    def test_connection(self):
        """测试数据库连接"""
        try:
            with self.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()

                if result and result[0] == 1:
                    logger.info("✅ 数据库连接测试成功")
                    return True
                else:
                    logger.error("❌ 数据库连接测试失败")
                    return False

        except Exception as e:
            logger.error(f"❌ 数据库连接测试异常: {e}")
            return False

    def initialize_database(self):
        """初始化数据库表结构"""
        try:
            self._create_database()
            return True
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            return False

# 全局数据库管理器实例
db_manager = SQLiteManager()

def get_db_connection():
    """获取数据库连接的便捷函数"""
    return db_manager.get_connection()

def get_db_connection_context():
    """获取数据库连接上下文管理器的便捷函数"""
    return db_manager.get_connection_context()

def test_database():
    """测试数据库连接的便捷函数"""
    return db_manager.test_connection()

def init_database():
    """初始化数据库的便捷函数"""
    return db_manager.initialize_database()

