# 🔧 防检测系统配置指南

## 📋 基础配置

### 1. 风险控制参数

```python
# 在 cursor_pro_anti_detection.py 中调整这些参数：

class CursorProAntiDetection:
    def __init__(self):
        self.success_rate_threshold = 0.7  # 成功率阈值 (70%)
        self.max_daily_registrations = 10  # 每日最大注册数
```

### 2. 行为模拟参数

```python
# 在 advanced_anti_detection.py 中调整：

def generate_realistic_timing(self, base_time: float = 1.0, variance: float = 0.5):
    # base_time: 基础时间 (秒)
    # variance: 变化范围 (秒)
```

### 3. 打字速度参数

```python
# 在 enhanced_new_signup.py 中调整：

def _human_input_field(self, element, text, field_name):
    typing_speed = random.uniform(0.06, 0.12)  # 打字速度范围
    think_pause_probability = 0.08  # 思考停顿概率
    error_probability = 0.02  # 打字错误概率
```

## ⚙️ 高级配置

### 1. 代理设置

```python
from advanced_anti_detection import AdvancedAntiDetection

detector = AdvancedAntiDetection()
driver = detector.create_stealth_driver(
    headless=False,
    proxy="http://your-proxy:port"  # 设置代理
)
```

### 2. 浏览器配置

```python
# 修改浏览器配置文件
profiles = detector.browser_profiles
# 可以添加自定义配置
```

### 3. 风险等级阈值

```python
# 风险评分规则：
# 0-30: 低风险 - 可以正常操作
# 31-60: 中风险 - 降低频率
# 61+: 高风险 - 停止操作
```

## 📊 监控和调试

### 1. 启用详细日志

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 查看统计信息

```python
from anti_detection_integration import get_registration_statistics

stats = get_registration_statistics()
print(stats)
```

### 3. 手动风险评估

```python
from cursor_pro_anti_detection import CursorProAntiDetection

detector = CursorProAntiDetection()
risk_info = detector.detector.check_detection_risk()
print(f"当前风险: {risk_info}")
```

## 🎯 最佳实践

1. **时间分散**: 不要在固定时间注册
2. **数量控制**: 每天不超过10个账户
3. **环境轮换**: 定期更换IP和浏览器
4. **监控反馈**: 关注成功率变化
5. **及时调整**: 根据效果调整参数

## ⚠️ 故障排除

### 问题1: 成功率突然下降
**解决方案**: 
- 检查IP是否被标记
- 降低注册频率
- 更换浏览器环境

### 问题2: 验证码无法通过
**解决方案**:
- 增加观察时间
- 检查网络连接
- 尝试更换IP

### 问题3: 页面加载异常
**解决方案**:
- 增加等待时间
- 检查浏览器版本
- 清理浏览器缓存
