# ⚠️ 免责声明 / DISCLAIMER

## 🇨🇳 中文版本

### 重要声明

本软件（Cursor Pro）仅供**学习和研究目的**使用。

### 使用条款

1. **教育用途**: 本软件仅用于学习编程技术、软件架构和开发实践
2. **研究目的**: 用于研究软件逆向工程、系统集成等技术领域
3. **禁止商业使用**: 严禁将本软件用于任何商业目的或盈利活动
4. **遵守法律**: 用户必须遵守所在地区的相关法律法规

### 风险提示

- ⚠️ 使用本软件可能违反Cursor官方服务条款
- ⚠️ 可能导致Cursor账户被封禁或限制
- ⚠️ 用户需自行承担使用风险和后果
- ⚠️ 建议支持正版软件，购买官方授权

### 免责条款

1. **无担保**: 本软件按"现状"提供，不提供任何明示或暗示的担保
2. **无责任**: 开发者不对使用本软件造成的任何损失承担责任
3. **自主选择**: 用户下载和使用本软件完全出于自愿
4. **法律后果**: 因使用本软件产生的法律后果由用户自行承担

### 建议

- 💡 **支持正版**: 建议购买Cursor官方授权版本
- 💡 **学习为主**: 将本软件作为学习工具，了解技术实现
- 💡 **合规使用**: 确保使用方式符合当地法律法规
- 💡 **风险评估**: 使用前请充分评估潜在风险

---

## 🇺🇸 English Version

### Important Notice

This software (Cursor Pro) is provided for **educational and research purposes only**.

### Terms of Use

1. **Educational Purpose**: This software is intended for learning programming techniques, software architecture, and development practices
2. **Research Purpose**: For studying reverse engineering, system integration, and related technical fields
3. **No Commercial Use**: Commercial use or profit-making activities are strictly prohibited
4. **Legal Compliance**: Users must comply with applicable laws and regulations in their jurisdiction

### Risk Warning

- ⚠️ Using this software may violate Cursor's official terms of service
- ⚠️ May result in Cursor account suspension or restrictions
- ⚠️ Users assume all risks and consequences of use
- ⚠️ We recommend supporting legitimate software by purchasing official licenses

### Disclaimer

1. **No Warranty**: This software is provided "as is" without any express or implied warranties
2. **No Liability**: Developers are not responsible for any losses caused by using this software
3. **Voluntary Use**: Users download and use this software entirely at their own discretion
4. **Legal Consequences**: Users are solely responsible for any legal consequences arising from use

### Recommendations

- 💡 **Support Official**: We recommend purchasing official Cursor licenses
- 💡 **Learning Focus**: Use this software as a learning tool to understand technical implementations
- 💡 **Compliant Use**: Ensure usage complies with local laws and regulations
- 💡 **Risk Assessment**: Thoroughly assess potential risks before use

---

## 📞 联系方式 / Contact

如有疑问，请通过以下方式联系：
For questions, please contact via:

- GitHub Issues: [项目地址]
- Email: [联系邮箱]

**最后更新 / Last Updated**: 2024-01-20

---

**通过下载、安装或使用本软件，您表示已阅读、理解并同意本免责声明的所有条款。**

**By downloading, installing, or using this software, you acknowledge that you have read, understood, and agree to all terms of this disclaimer.**
