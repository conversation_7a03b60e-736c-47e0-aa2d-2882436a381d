#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 静默启动器
后台启动前端和后端服务，不显示任何窗口
"""

import os
import sys
import time
import subprocess
from pathlib import Path

class CursorProSilentLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        
    def get_exe_dir(self):
        """获取exe文件所在目录"""
        if getattr(sys, 'frozen', False):
            # 打包后的exe
            return Path(sys.executable).parent
        else:
            # 开发环境
            return Path(__file__).parent
    
    def find_backend_exe(self):
        """查找后端exe文件"""
        exe_dir = self.get_exe_dir()
        possible_paths = [
            exe_dir / "resources" / "python-backend" / "cursor-backend.exe",
            exe_dir / "cursor-backend.exe",
            exe_dir / "backend" / "cursor-backend.exe"
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        return None
    
    def find_frontend_exe(self):
        """查找前端exe文件"""
        exe_dir = self.get_exe_dir()
        possible_paths = [
            exe_dir / "Cursor Pro.exe",
            exe_dir / "cursor-pro.exe",
            exe_dir / "frontend" / "Cursor Pro.exe"
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        return None
    
    def is_backend_running(self):
        """检查后端是否已经在运行"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', 8080))
            sock.close()
            return result == 0
        except:
            return False
    
    def start_backend(self):
        """静默启动后端服务"""
        # 检查后端是否已经在运行
        if self.is_backend_running():
            return True
            
        backend_exe = self.find_backend_exe()
        if not backend_exe:
            return False
        
        try:
            # 静默启动后端，隐藏控制台窗口
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            self.backend_process = subprocess.Popen(
                [backend_exe],
                cwd=str(Path(backend_exe).parent),
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            return True
            
        except Exception as e:
            return False
    
    def start_frontend(self):
        """启动前端应用"""
        frontend_exe = self.find_frontend_exe()
        if not frontend_exe:
            return False
        
        try:
            # 正常启动前端，显示窗口
            self.frontend_process = subprocess.Popen([frontend_exe])
            return True
            
        except Exception as e:
            return False
    
    def wait_for_backend(self, timeout=10):
        """等待后端服务启动"""
        for i in range(timeout):
            if self.is_backend_running():
                return True
            time.sleep(1)
        return False
    
    def run(self):
        """运行静默启动器"""
        # 1. 启动后端服务
        if not self.start_backend():
            # 如果启动失败，静默退出
            sys.exit(1)
        
        # 2. 等待后端服务启动
        if not self.wait_for_backend():
            # 如果后端启动超时，静默退出
            sys.exit(1)
        
        # 3. 启动前端应用
        if not self.start_frontend():
            # 如果前端启动失败，静默退出
            sys.exit(1)
        
        # 4. 启动完成，退出启动器
        sys.exit(0)

def main():
    launcher = CursorProSilentLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
