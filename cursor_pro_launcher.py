#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 启动器
一键启动前端和后端服务
"""

import os
import sys
import time
import subprocess
from pathlib import Path

class CursorProLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        
    def get_exe_dir(self):
        """获取exe文件所在目录"""
        if getattr(sys, 'frozen', False):
            # 打包后的exe
            return Path(sys.executable).parent
        else:
            # 开发环境
            return Path(__file__).parent
    
    def find_backend_exe(self):
        """查找后端exe文件"""
        exe_dir = self.get_exe_dir()
        possible_paths = [
            exe_dir / "resources" / "python-backend" / "cursor-backend.exe",
            exe_dir / "cursor-backend.exe",
            exe_dir / "backend" / "cursor-backend.exe"
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        return None
    
    def find_frontend_exe(self):
        """查找前端exe文件"""
        exe_dir = self.get_exe_dir()
        possible_paths = [
            exe_dir / "Cursor Pro.exe",
            exe_dir / "cursor-pro.exe",
            exe_dir / "frontend" / "Cursor Pro.exe"
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        return None
    
    def start_backend(self):
        """静默启动后端服务"""
        backend_exe = self.find_backend_exe()
        if not backend_exe:
            return False

        try:
            # 静默启动后端，隐藏控制台窗口
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            self.backend_process = subprocess.Popen(
                [backend_exe],
                cwd=str(Path(backend_exe).parent),
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            return True

        except Exception:
            return False
    
    def start_frontend(self):
        """启动前端应用"""
        frontend_exe = self.find_frontend_exe()
        if not frontend_exe:
            return False

        try:
            self.frontend_process = subprocess.Popen([frontend_exe])
            return True

        except Exception:
            return False

    def cleanup(self):
        """清理进程"""
        if self.backend_process and self.backend_process.poll() is None:
            try:
                self.backend_process.terminate()
                # 等待进程结束，如果超时则强制杀死
                try:
                    self.backend_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.backend_process.kill()
            except Exception:
                pass

    def wait_for_frontend(self):
        """等待前端进程结束"""
        if self.frontend_process:
            try:
                self.frontend_process.wait()
            except Exception:
                pass
    



def main():
    launcher = CursorProLauncher()

    try:
        # 启动后端服务
        if not launcher.start_backend():
            sys.exit(1)

        # 等待后端初始化
        time.sleep(3)

        # 启动前端应用
        if not launcher.start_frontend():
            launcher.cleanup()
            sys.exit(1)

        # 等待前端进程结束
        launcher.wait_for_frontend()

        # 前端关闭后，清理后端进程
        launcher.cleanup()

    except KeyboardInterrupt:
        # 用户按Ctrl+C时清理进程
        launcher.cleanup()
        sys.exit(0)
    except Exception:
        # 异常时清理进程
        launcher.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
