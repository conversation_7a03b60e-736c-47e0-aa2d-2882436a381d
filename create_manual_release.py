#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动创建 Cursor Pro 发布包
使用现有的构建文件创建完整的发布包
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_release_directory():
    """创建发布目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = Path(f'release_cursor_pro_manual_{timestamp}')
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    print(f"📁 创建发布目录: {release_dir}")
    return release_dir

def copy_existing_components(release_dir):
    """复制现有的组件"""
    print("\n📦 复制现有组件...")
    
    # 创建子目录
    launcher_dir = release_dir / 'launcher'
    app_dir = release_dir / 'app'
    docs_dir = release_dir / 'docs'
    tools_dir = release_dir / 'tools'
    
    launcher_dir.mkdir()
    app_dir.mkdir()
    docs_dir.mkdir()
    tools_dir.mkdir()
    
    # 复制启动器
    if Path('dist/Cursor Pro 启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 启动器.exe', launcher_dir / 'Cursor Pro 启动器.exe')
        print("✅ 复制启动器")
    
    if Path('dist/Cursor Pro 智能启动器.exe').exists():
        shutil.copy2('dist/Cursor Pro 智能启动器.exe', launcher_dir / 'Cursor Pro 智能启动器.exe')
        print("✅ 复制智能启动器")
    
    # 复制主应用
    if Path('cursor_pro').exists():
        shutil.copytree('cursor_pro', app_dir / 'cursor_pro', dirs_exist_ok=True)
        print("✅ 复制主应用")
    
    # 复制文档
    if Path('docs').exists():
        shutil.copytree('docs', docs_dir, dirs_exist_ok=True)
        print("✅ 复制文档")
    
    # 复制工具
    if Path('cleanup_cursor_backend.bat').exists():
        shutil.copy2('cleanup_cursor_backend.bat', tools_dir / 'cleanup_cursor_backend.bat')
        print("✅ 复制清理工具")
    
    # 复制其他重要文件
    important_files = [
        'README.md',
        '快速上手.md',
        '文档目录.md',
        'requirements.txt'
    ]
    
    for file in important_files:
        if Path(file).exists():
            shutil.copy2(file, release_dir / file)
            print(f"✅ 复制文件: {file}")

def create_startup_script(release_dir):
    """创建启动脚本"""
    startup_content = '''@echo off
chcp 65001 >nul
title Cursor Pro - 启动器
color 0A

echo ========================================
echo  Cursor Pro - 增强版启动器
echo ========================================
echo.

echo 请选择启动方式:
echo [1] 使用智能启动器 (推荐)
echo [2] 使用标准启动器
echo [3] 直接启动应用
echo [4] 清理后端进程
echo [0] 退出
echo.

set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" (
    echo 启动智能启动器...
    start "" "launcher\\Cursor Pro 智能启动器.exe"
) else if "%choice%"=="2" (
    echo 启动标准启动器...
    start "" "launcher\\Cursor Pro 启动器.exe"
) else if "%choice%"=="3" (
    echo 直接启动应用...
    start "" "app\\cursor_pro\\Cursor Pro.exe"
) else if "%choice%"=="4" (
    echo 清理后端进程...
    call "tools\\cleanup_cursor_backend.bat"
    pause
    goto :start
) else if "%choice%"=="0" (
    echo 退出程序
    exit /b
) else (
    echo 无效选择，请重新输入
    pause
    goto :start
)

echo.
echo 程序已启动，您可以关闭此窗口
pause
'''
    
    startup_path = release_dir / 'start.bat'
    with open(startup_path, 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ 创建启动脚本: start.bat")

def create_install_script(release_dir):
    """创建安装脚本"""
    install_content = '''@echo off
chcp 65001 >nul
title Cursor Pro - 安装程序
color 0B

echo ========================================
echo  Cursor Pro - 安装程序
echo ========================================
echo.

echo 此程序将安装 Cursor Pro 到您的系统
echo.

set "INSTALL_DIR=C:\\Program Files\\Cursor Pro"

echo 安装目录: %INSTALL_DIR%
echo.

set /p confirm="确认安装? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo 安装已取消
    pause
    exit /b
)

echo.
echo 正在安装...

REM 创建安装目录
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

REM 复制文件
echo 复制程序文件...
xcopy /E /I /Y "app\\*" "%INSTALL_DIR%\\app\\"
xcopy /E /I /Y "launcher\\*" "%INSTALL_DIR%\\launcher\\"
xcopy /E /I /Y "tools\\*" "%INSTALL_DIR%\\tools\\"
xcopy /E /I /Y "docs\\*" "%INSTALL_DIR%\\docs\\"

copy /Y "*.md" "%INSTALL_DIR%\\" >nul 2>&1
copy /Y "*.txt" "%INSTALL_DIR%\\" >nul 2>&1

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Cursor Pro.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\launcher\\Cursor Pro 智能启动器.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo ========================================
echo  安装完成!
echo ========================================
echo.
echo Cursor Pro 已成功安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
echo 使用方法:
echo 1. 双击桌面快捷方式启动
echo 2. 或运行: %INSTALL_DIR%\\launcher\\Cursor Pro 智能启动器.exe
echo.
pause
'''
    
    install_path = release_dir / 'install.bat'
    with open(install_path, 'w', encoding='utf-8') as f:
        f.write(install_content)
    
    print("✅ 创建安装脚本: install.bat")

def create_readme(release_dir):
    """创建发布说明"""
    readme_content = '''# Cursor Pro - 完整发布包

## 📦 包含内容

### 🚀 启动器
- `launcher/Cursor Pro 智能启动器.exe` - 智能启动器 (推荐)
- `launcher/Cursor Pro 启动器.exe` - 标准启动器

### 🖥️ 主应用
- `app/cursor_pro/` - 完整的 Cursor Pro 应用程序
- `app/cursor_pro/Cursor Pro.exe` - 主程序
- `app/cursor_pro/resources/python-backend/` - 后端服务

### 🛠️ 工具
- `tools/cleanup_cursor_backend.bat` - 后端进程清理工具

### 📚 文档
- `docs/` - 完整文档和指南
- `README.md` - 项目说明
- `快速上手.md` - 快速开始指南

## 🚀 快速开始

### 方法1: 自动安装 (推荐)
1. 双击 `install.bat` 运行安装程序
2. 按照提示完成安装
3. 使用桌面快捷方式启动

### 方法2: 便携运行
1. 双击 `start.bat` 选择启动方式
2. 推荐选择 "智能启动器"

### 方法3: 手动启动
1. 运行 `launcher/Cursor Pro 智能启动器.exe`
2. 或直接运行 `app/cursor_pro/Cursor Pro.exe`

## 🔧 故障排除

如果遇到后端进程残留问题:
1. 运行 `tools/cleanup_cursor_backend.bat`
2. 或使用任务管理器手动结束 `cursor-backend.exe` 进程

## 📖 详细文档

查看 `docs/` 目录获取:
- 完整安装指南
- 配置说明
- 技术支持
- 常见问题解答

## ⚠️ 重要提醒

- 本软件仅供学习和研究使用
- 请阅读相关文档了解使用方法
- 如遇问题请查看技术支持文档

---

**Cursor Pro Enhanced Edition**
*智能启动 · 稳定运行 · 完整功能*
'''
    
    readme_path = release_dir / 'README_发布说明.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建发布说明: README_发布说明.txt")

def create_zip_package(release_dir):
    """创建 ZIP 压缩包"""
    print("\n🗜️ 创建 ZIP 压缩包...")
    
    zip_name = f'{release_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("=" * 60)
    print("🏗️  Cursor Pro - 手动发布包创建工具")
    print("=" * 60)
    
    # 创建发布目录
    release_dir = create_release_directory()
    
    # 复制现有组件
    copy_existing_components(release_dir)
    
    # 创建脚本和文档
    create_startup_script(release_dir)
    create_install_script(release_dir)
    create_readme(release_dir)
    
    # 创建压缩包
    zip_file = create_zip_package(release_dir)
    
    print("\n" + "=" * 60)
    print("🎉 手动发布包创建成功!")
    print("=" * 60)
    print(f"📁 发布目录: {release_dir}")
    print(f"📦 压缩包: {zip_file}")
    print("\n📋 使用说明:")
    print("1. 解压 ZIP 文件")
    print("2. 运行 install.bat 进行安装")
    print("3. 或运行 start.bat 便携使用")
    print("\n🚀 用户可以:")
    print("- 选择安装到系统或便携运行")
    print("- 使用智能启动器自动管理进程")
    print("- 查看完整文档获取帮助")

if __name__ == "__main__":
    main()
